<template>
    <div>
        <uv-vtabs
            :list="i.list"
            hdHeight="462rpx"
            barWidth="170rpx"
            barBgColor="#F7FAF9"
            :height="height"
            :barItemStyle="{
                fontSize: '28rpx',
                fontWeight: 'bold',
                color: '#262626'
            }"
            :barItemActiveStyle="{
                fontSize: '28rpx',
                color: '#00C389',
                fontWeight: 'bold'
            }"
            :barItemActiveLineStyle="{
                backgroundColor: '#00C389',
                width: '6rpx',
                height: '40rpx',
                top: '32rpx'
            }"
            :contentStyle="{ position: 'relative', flex: 1, overflow: 'hidden' }"
            :barItemBadgeStyle="{
                right: '0px',
                top: '5px',
                background: '#00c389',
                fontWeight: 'normal',
                borderRadius: '100px'
            }"
        >
            <template v-for="(item, index) in i.list" :key="item.mealSetTypeId">
                <view
                    class="meal_section absoult_box"
                    :style="{
                        bottom: state.days[state.active].isTodayOrder == 0 ? 'auto' : 0,
                        top: state.days[state.active].isTodayOrder == 0 ? 0 : 'auto'
                    }"
                    v-if="index == 0 && isBefore"
                >
                    <!-- 如果已点餐并且今日不预定 -->
                    <view class="meal_header" v-if="state.days[state.active].isOrderedFood == 1 && state.days[state.active].isTodayOrder == 0">
                        <text class="meal_title">当日不预定</text>
                        <uni-icons type="checkbox-filled" size="28" color="var(--primary-color)"></uni-icons>
                    </view>
                    <!-- 如果未点餐 -->
                    <view class="meal_header" @click="handleReserve" v-if="state.days[state.active].isOrderedFood == 0">
                        <text class="meal_title">当日不预定</text>
                        <uni-icons v-if="state.days[state.active].isCheck" type="checkbox-filled" size="28" color="var(--primary-color)"></uni-icons>
                        <view class="circle" v-else></view>
                    </view>
                </view>
                <view class="tip_box" v-if="index == 0 && state.days[state.active].deadline">
                    <uni-icons type="info-filled" size="16" color="#FFA052" style="margin-right: 10rpx"></uni-icons>
                    预定套餐截止时间：{{ state.days[state.active].deadline }}
                </view>
                <uv-vtabs-item :index="item.mealSetTypeId">
                    <view class="type_title" v-if="state.days[state.active].isTodayOrder != false">{{ item.name }}</view>
                    <view
                        :style="{
                            padding: '0 20rpx',
                            paddingBottom: index === i.list.length - 1 ? '108rpx' : '0rpx'
                        }"
                    >
                        <view class="meal_section" v-for="child in item.children" :key="child.name">
                            <template v-if="child.mealSetDishes.length || child.singlePointDishes.lengt">
                                <view class="meal_header">
                                    <view class="meal_title">{{ child.name }}</view>
                                    <view v-if="!state.days[state.active].isOrderedFood && isBefore">
                                        <template v-if="child.isCheck">
                                            <uni-icons type="checkbox-filled" size="28" color="#cbcbcb" v-if="state.days[state.active].isCheck"></uni-icons>
                                            <uni-icons type="checkbox-filled" size="28" color="var(--primary-color)" v-else @click="handleCheck(child, item.children)"></uni-icons>
                                        </template>
                                        <template v-else>
                                            <view :class="['circle', state.days[state.active].isCheck ? 'poiner_none' : '']" @click="handleCheck(child, item.children)"></view>
                                        </template>
                                    </view>
                                </view>
                                <view class="dish_list">
                                    <view class="dish_item" v-for="base in child.mealSetDishes" :key="base.dishId">
                                        <image class="dish_image" :src="base.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                                        <view class="dish_content">
                                            <text class="dish_name">{{ base.dishName }}</text>
                                            <text class="dish_price">¥{{ base.dishPrice }}</text>
                                        </view>
                                    </view>
                                </view>
                                <view class="extra_section" v-if="child.singlePointDishes.length > 0">
                                    <text class="extra_title">自选额外菜品:</text>
                                    <view class="dish_list">
                                        <view class="dish_item" v-for="extra in child.singlePointDishes" :key="extra.dishId">
                                            <image class="dish_image" :src="extra.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                                            <view class="dish_content">
                                                <text class="dish_name">{{ extra.dishName }}</text>
                                                <view class="dish_price"
                                                    >¥{{ extra.dishPrice }}
                                                    <view v-if="!state.days[state.active].isOrderedFood && isBefore">
                                                        <template v-if="extra.isCheck">
                                                            <uni-icons type="checkbox-filled" size="28" color="#cbcbcb" v-if="state.days[state.active].isCheck"></uni-icons>
                                                            <uni-icons type="checkbox-filled" size="28" color="var(--primary-color)" v-else @click="handleCheck(extra)"></uni-icons>
                                                        </template>
                                                        <template v-else>
                                                            <view :class="['circle', state.days[state.active].isCheck ? 'poiner_none' : '']" @click="handleCheck(extra)"></view>
                                                        </template>
                                                    </view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view class="total_price">
                                    <text :style="{ color: child.isEat == 0 ? '#F5222D' : 'var(--primary-color)' }">
                                        <template v-if="child.isEat !== null">
                                            <text v-if="child.isEat == 1">{{ child.eatTime }}</text>
                                            <text>{{ child.isEat == 0 ? "未就餐" : "已就餐" }}</text>
                                        </template>
                                    </text>
                                    <text>合计：{{ totalPrice(child) }}元</text>
                                </view>
                            </template>
                        </view>
                    </view>
                </uv-vtabs-item>
            </template>
        </uv-vtabs>
    </div>
</template>

<script setup></script>

<style lang="scss" scoped></style>
