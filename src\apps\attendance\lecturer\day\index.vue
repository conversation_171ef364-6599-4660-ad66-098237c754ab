<template>
    <view class="day_container_box">
        <view :class="{ minH: packUp }">
            <!-- 插入模式 -->
            <uni-calendar id="calendar" class="uni_calendar" :showMonth="false" :date="date" @change="change" @monthSwitch="monthSwitch" />
        </view>
        <view class="img_box" @click="packUp = !packUp"><image :src="!packUp ? 'https://alicdn.1d1j.cn/announcement/20230712/88b473beae484f2690af4ee585c930c2.png' : 'https://alicdn.1d1j.cn/announcement/20230712/03bc41c4c81949109683163670676f41.png'" /></view>

        <view class="mian_box">
            <view class="top">
                <text class="left text">课程考勤</text>
            </view>
        </view>
        <Swiper v-if="state.swiperList.length" v-model:current="current" @change="swiperChange" :list="state.swiperList"></Swiper>

        <view class="msg_box">
            <ListMsgBox :list="dataList"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>
    </view>
</template>

<script setup>
import Swiper from "./components/swiper.vue"
import ListMsgBox from "./components/listMsgBox.vue"
import { onReachBottom } from "@dcloudio/uni-app"
import useQueryList from "../../hook/useQueryList.js"
import dayjs from "dayjs"

const { getList, dataList, pageNo, status } = useQueryList()

const state = reactive({
    list: [],
    swiperList: []
})

let packUp = ref(true)
let date = ref(dayjs().format("YYYY-MM-DD"))
let current = ref(0)

// 获取日统计头部
async function getDayStatistics() {
    const params = {
        startDate: date.value
    }
    const { data = [] } = await http.post("/app/teach/attendance/timetableDay", params)
    state.swiperList = data

    const argument = {}
    if (state.swiperList.length) {
        argument.attendanceId = state.swiperList[0].attendanceId
        getList(getDayList, argument)
    }
}
getDayStatistics()

function getDayList(params) {
    return http.post("/app/teach/attendance/timetableDayPage", params)
}

onReachBottom(() => {
    const params = {
        attendanceId: state.swiperList[current.value].attendanceId
    }
    getList(getDayList, params)
})

const change = (e) => {
    date.value = e.fulldate
    dataList.value = []
    pageNo.value = 1
    getDayStatistics()
}

const monthSwitch = (e) => {
    console.log("monthSwitchs 返回:", e)
}

const swiperChange = (val) => {
    dataList.value = []
    pageNo.value = 1
    const params = {
        attendanceId: state.swiperList[current.value].attendanceId
    }
    getList(getDayList, params)
}
</script>

<style lang="scss">
.day_container_box {
    .minH {
        height: 300rpx;
        overflow: hidden;
    }
    .img_box {
        text-align: center;
        background-color: var(--primary-bg-color);
        padding: 28rpx;
        image {
            width: 56rpx;
            height: 16rpx;
        }
    }
    .uni_calendar {
        :deep(.uni-calendar__header-text) {
            font-size: 18px;
            font-weight: 600;
        }
        :deep(.uni-calendar__content) {
            background-color: var(--primary-bg-color);
        }
        :deep(.uni-calendar-item--isDay) {
            border-radius: 50%;
        }
        :deep(.uni-calendar-item--checked) {
            border-radius: 50%;
        }
    }
    .mian_box {
        padding: 0 30rpx;
        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100rpx;
            border-bottom: 1rpx solid #d9d9d9;
            .left {
                text-align: left;
                width: 170rpx;
            }
            .right {
                text-align: right;
                width: 250rpx;
            }
            .text {
                position: relative;
                padding-right: 30rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                font-weight: 600;
            }
        }
    }
    .msg_box {
        padding-bottom: 20rpx;
    }
}
</style>
