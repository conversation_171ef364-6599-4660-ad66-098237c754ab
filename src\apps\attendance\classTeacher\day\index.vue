<template>
    <view class="day_container_box">
        <view :class="{ minH: packUp }">
            <!-- 插入模式 -->
            <uni-calendar class="uni_calendar" :showMonth="false" :date="date" @change="change" />
        </view>
        <view class="img_box" @click="packUp = !packUp"><image :src="!packUp ? 'https://alicdn.1d1j.cn/announcement/20230712/88b473beae484f2690af4ee585c930c2.png' : 'https://alicdn.1d1j.cn/announcement/20230712/03bc41c4c81949109683163670676f41.png'" /></view>

        <view class="mian_box">
            <view class="top">
                <text class="left text" @click="attendSchool">{{ state.typeData.name }}</text>
                <text class="right text" v-if="state.checkList.length && state.typeData.value !== 2" @click="attendName">{{ state.checkData.name }}</text>
            </view>
        </view>
        <Swiper v-if="state.swiperList.length" :list="state.swiperList"></Swiper>

        <view class="msg_box">
            <ListMsgBox :list="dataList"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>

        <select-popup ref="selPopO" title="选择考勤类型" :list="state.typeList" @closePopup="(val) => closePopup(val, 0)" />

        <select-popup ref="selPopT" title="选择考勤 " :list="state.checkList" @closePopup="(val) => closePopup(val, 1)" />
    </view>
</template>

<script setup>
import SelectPopup from "../../components/selectPopup.vue"
import Swiper from "./components/swiper.vue"
import ListMsgBox from "./components/listMsgBox.vue"
import { onReachBottom, onLoad } from "@dcloudio/uni-app"
import { reactive, ref } from "vue"
import useQueryList from "../../hook/useQueryList.js"
import Http from "@/utils/http"
import dayjs from "dayjs"

const { getList, dataList, pageNo, status } = useQueryList()

const state = reactive({
    list: [],
    swiperList: [],
    typeList: [
        { name: "出入校考勤", value: 0, isCheck: true },
        { name: "事件考勤", value: 1 },
        { name: "课程考勤", value: 2 }
    ],
    checkList: [],
    typeData: { name: "出入校考勤", value: 0 },
    checkData: {},
    classesId: ""
})

let packUp = ref(true)
let date = ref(dayjs().format("YYYY-MM-DD"))

// 获取考勤列表
const getEventList = async () => {
    const params = {
        classesId: state.classesId,
        startDate: date.value,
        endDate: date.value,
        type: state.typeData.value
    }

    const { data } = await Http.post("/app/master/attendance/getEventList", params)
    state.checkList = data.map((i) => ({ ...i, value: i.id }))

    if (state.checkList.length) {
        state.checkData = state.checkList[0]
        state.checkData.isCheck = true
    }
    getDayStatistics()
}

// 获取日统计头部
async function getDayStatistics() {
    const params = {
        classesId: state.classesId,
        startDate: date.value,
        type: state.typeData.value,
        attendanceId: state.checkData.value === 2 ? state.checkData.value : ""
    }

    const { data = [] } = await Http.post("/app/master/attendance/dayStatistics", params)
    state.swiperList = data

    getList(getDayList, params)
}

function getDayList(params) {
    return Http.post("/app/master/attendance/dayStatisticsPage", params)
}

onLoad((options) => {
    state.classesId = options.classesId
    getEventList()
})

onReachBottom(() => {
    const params = {
        classesId: state.classesId,
        startDate: date.value,
        type: state.typeData.value,
        attendanceId: state.checkData.value === 2 ? state.checkData.value : ""
    }
    getList(getDayList, params)
})

const change = (e) => {
    date.value = e.fulldate
    dataList.value = []
    pageNo.value = 1
    getDayStatistics()
}

// 考勤事件
const selPopO = ref(null)
const attendSchool = () => {
    selPopO.value.open()
}

// 对应考勤名称
const selPopT = ref(null)
const attendName = () => {
    selPopT.value.open()
}

const closePopup = (val, flag) => {
    dataList.value = []
    pageNo.value = 1
    if (flag === 0) {
        state.typeData = val
        getEventList()
    } else {
        state.checkData = val
        getDayStatistics()
    }
}
</script>

<style lang="scss">
.day_container_box {
    .minH {
        height: 300rpx;
        overflow: hidden;
    }
    .img_box {
        text-align: center;
        background-color: var(--primary-bg-color);
        padding: 28rpx;
        image {
            width: 56rpx;
            height: 16rpx;
        }
    }
    .uni_calendar {
        :deep(.uni-calendar__header-text) {
            font-size: 18px;
            font-weight: 600;
        }
        :deep(.uni-calendar__content) {
            background-color: var(--primary-bg-color);
        }
        :deep(.uni-calendar-item--isDay) {
            border-radius: 50%;
        }
        :deep(.uni-calendar-item--checked) {
            border-radius: 50%;
        }
    }
    .mian_box {
        padding: 0 30rpx;
        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100rpx;
            border-bottom: 1rpx solid #d9d9d9;
            .left {
                text-align: left;
                width: 170rpx;
            }
            .right {
                text-align: right;
                width: 250rpx;
            }
            .text {
                position: relative;
                padding-right: 30rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                -o-text-overflow: ellipsis;
                &::after {
                    content: "";
                    width: 0;
                    height: 0;
                    display: inline-block;
                    border: 14rpx solid transparent;
                    border-top-color: var(--primary-color);
                    position: absolute;
                    right: 0rpx;
                    top: 15rpx;
                }
            }
        }
    }
    .msg_box {
        padding-bottom: 20rpx;
    }
}
</style>
