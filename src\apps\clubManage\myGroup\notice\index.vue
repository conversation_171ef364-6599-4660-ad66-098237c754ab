<template>
    <view class="notice_container">
        <z-paging ref="paging" class="container" v-model="dataList" @query="queryList" :auto="false">
            <view class="item_box" :style="{ filter: !item.isRead ? 'opacity(1)' : 'opacity(0.7)' }" v-for="(item, index) in dataList" :key="index" @click="handleClick(item)">
                <view class="top">
                    <view class="l">{{ item.title }}</view>
                    <view class="r">
                        <text class="drop" v-if="!item.isRead"></text>
                        {{ item.publishedAt && item.publishedAt.replace("T", " ") }}
                    </view>
                </view>
                <view class="bottom">{{ item.content }}</view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" :isMargin="true" />
            </template>
        </z-paging>
    </view>
</template>
<script setup>
const paging = ref(null)
const dataList = ref([])

const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize
    }
    http.post("/app/club/notice/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

onShow(() => {
    nextTick(() => {
        paging.value.reload()
    })
})

const handleClick = (item) => {
    navigateTo({
        url: "/apps/clubManage/myGroup/noticeDetail/index",
        query: { ...item },
        success: (res) => {
            // 如果未读跳转成功之后标记已读
            if (!item.isRead) {
                http.post("/app/club/notice/mark-as-read", { ids: [item.id] })
            }
        }
    })
}
</script>
<style lang="scss" scoped>
.notice_container {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
}
.container {
    padding: 20rpx 30rpx;
    .item_box {
        padding: 30rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-sizing: border-box;
        margin-bottom: 20rpx;
        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30rpx;
            .l {
                font-size: 30rpx;
                font-weight: 600;
                color: #333333;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .r {
                flex-shrink: 0;
                color: #999;
                font-size: 24rpx;
                display: flex;
                align-items: center;
                .drop {
                    display: inline-block;
                    width: 12rpx;
                    height: 12rpx;
                    background-color: #f5222d;
                    border-radius: 50%;
                    margin-right: 8rpx;
                }
            }
        }
        .bottom {
            max-height: 72rpx;
            font-size: 26rpx;
            line-height: 40rpx;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }
}
</style>
