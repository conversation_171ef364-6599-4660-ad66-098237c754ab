<template>
    <view class="container_box">
        <z-paging>
            <view class="main">
                <view class="header">
                    <view class="role">
                        <template v-if="state.type == 'notification'"> 系 </template>
                        <template v-else>
                            {{ state.authorName && state.authorName.slice(0, 1) }}
                        </template>
                    </view>
                    <view class="right">
                        <view class="item_top_box">
                            <view class="l">
                                <view class="text_top"> {{ state.type == "notification" ? "系统通知" : state.authorName }} </view>
                                <view class="text_btm">{{ state.publishedAt && state.publishedAt.replace("T", " ") }}</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="title">{{ state.title }}</view>
                <view class="content">{{ state.content }}</view>
            </view>
            <template #bottom v-if="state.canEdit && state.type == 'announcement'">
                <view class="footer">
                    <button class="btn" @click="handleEdit">编辑公告</button>
                </view>
            </template>
        </z-paging>
    </view>
</template>
<script setup>
const state = ref({
    authorName: "",
    publishedAt: ""
})

function getList({ id, clubId }) {
    uni.showLoading({
        title: "Loading..."
    })
    http.post("/app/club/notice/announcement/list", { clubId })
        .then((res) => {
            const data = res.data ?? []
            const result = data.find((item) => item.id == id)
            if (result) {
                state.value = result
            }
        })
        .finally(() => {
            uni.hideLoading()
        })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    // TODO: 通过url传参会改变数据类型，参数过长会被截取，缺少获取详情接口，暂时先这样处理。
    // type 表示从社团管理跳转过来
    if (options.type == "clubManage") {
        getList(options)
    } else {
        if ("canEdit" in options) options.canEdit = Boolean(options.canEdit)
        if ("isRead" in options) options.isRead = Boolean(options.isRead)

        Object.assign(state.value, options)
    }
})
const handleEdit = () => {
    navigateTo({
        url: "/apps/clubManage/myGroup/editNotice/index",
        query: { data: encodeURIComponent(JSON.stringify(state.value)) }
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
    .main {
        padding: 0 30rpx;
        background-color: $uni-text-color-inverse;
        .header {
            display: flex;
            padding: 30rpx 0;
            border-bottom: 2rpx solid #d9d9d9;
            .role {
                width: 96rpx;
                height: 96rpx;
                border-radius: 50%;
                overflow: hidden;
                flex-shrink: 0;
                margin-right: 24rpx;
                background-color: var(--primary-color);
                font-size: 34rpx;
                color: $uni-text-color-inverse;
                line-height: 96rpx;
                text-align: center;
            }
            .right {
                flex: 1;
                overflow: hidden;
                .item_top_box {
                    display: flex;
                    align-items: center;
                    height: 100rpx;
                    .l {
                        flex: 1;
                        overflow: hidden;
                        .text_top {
                            font-size: 32rpx;
                            color: #262626;
                            font-weight: 600;
                            margin-bottom: 8rpx;
                            overflow: hidden;
                        }
                        .text_btm {
                            font-size: 24rpx;
                            color: #999;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }
                    }
                }
            }
        }
        .title {
            font-size: 30rpx;
            color: #333;
            font-weight: 600;
            padding: 30rpx 0;
            line-height: 42rpx;
        }
        .content {
            font-size: 28rpx;
            color: #333;
            padding-bottom: 200rpx;
            line-height: 40rpx;
        }
    }
    .footer {
        position: fixed;
        bottom: 0;
        width: calc(100% - 60rpx);
        padding: 0 30rpx;
        background: $uni-bg-color;
        .btn {
            font-size: 32rpx;
            color: $uni-text-color-inverse;
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            background: var(--primary-color);
            width: 100%;
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            &:after {
                border: none;
            }
        }
    }
}
</style>
