<template>
    <ul class="m-drag" :style="[areaStyles]">
        <li
            v-for="(item, index) in state.newList"
            :key="index"
            :data-oindex="index"
            class="m-drag-item"
            :class="{ active: state.currentIndex === index }"
            :style="{
                top: _itemYList(index),
                ...props.dragItemStyle
            }"
        >
            <!-- <span style="color: red" @click="handleDelete(index)">删除</span> -->
            <slot :item="item" :index="index" />
            <!-- css实现拖拽图标 -->
            <div class="icon" @touchstart="touchStart($event, index)" @touchmove="touchMove" @touchend="touchEnd">
                <slot name="iconText" :item="item" :index="index" />
                <uni-icons class="bars" type="bars" size="20"></uni-icons>
            </div>
        </li>
    </ul>
</template>

<script setup>
import { reactive, watch, nextTick, triggerRef, getCurrentInstance } from "vue"
const emits = defineEmits(["change", "emitDelete"])

const app = getCurrentInstance()
const props = defineProps({
    // 每一项item高度，必须
    itemHeight: {
        type: Number,
        required: true
    },
    // 数据列表，必须
    list: {
        type: Array,
        required: true
    },
    // 是否只读
    readonly: {
        type: Boolean,
        default: false
    },
    // 拖拽样式
    dragItemStyle: {
        type: Object,
        default: () => {
            return {}
        }
    }
})

const state = reactive({
    // 数据
    newList: [],
    // 初始数据
    initialList: [],
    // 记录所有item的初始坐标
    initialItemYList: [],
    // 坐标数据
    itemYList: [],
    // 记录当前手指的垂直方向的坐标
    touchY: 0,
    // 记录当前操作的item数据
    currentItemY: {},
    // 当前操作的item的下标
    currentIndex: -1
})
const isTouchStart = ref(false)
const contentHeights = ref({})
// 计算总高度
const areaStyles = computed(() => {
    let height = 0
    // debugger
    Object.values(contentHeights.value)?.forEach((key) => {
        height += key
    })
    return { height: height + "px" }
})

// 删除
function handleDelete(index) {
    contentHeights.value = {}
    emits("emitDelete", index)
}
// 高度测量函数
function measureContentHeight(oindex) {
    nextTick(() => {
        const query = uni.createSelectorQuery().in(app.proxy)
        query
            .select(`.m-drag-item[data-oindex="${oindex}"]`)
            .boundingClientRect((res) => {
                if (res?.height) {
                    contentHeights.value[oindex] = res.height
                    triggerRef(state.newList) // 触发视图更新
                }
            })
            .exec()
    })
}
const _itemYList = computed(() => {
    return (idx) => {
        console.log(contentHeights.value)
        if (Object.values(contentHeights.value)?.length) {
            let y = 0
            for (let i = 0; i < idx; i++) {
                y += contentHeights.value[i] || 0
            }
            return y + "px"
        }
        return "100px"
    }
})
function updateList(v) {
    state.newList = v.map((content, i) => {
        measureContentHeight(i) // 初始化时测量
        return content
    })
}
watch(
    () => props.list,
    (val) => {
        if (!val?.length) return
        // 获取数据列表
        state.newList = [...val]
        // 记录初始数据
        state.initialList = [...val]
        // 获取所有item的初始坐标
        state.initialItemYList = getItemsY()
        // 初始化坐标
        state.itemYList = getItemsY()
        nextTick(() => {
            updateList(props.list)
        })
    },
    {
        immediate: true,
        deep: true
    }
)

/** @初始化各个item的坐标 **/
function getItemsY() {
    return props.list.map((item, i) => {
        return {
            top: i * props.itemHeight
        }
    })
}

/** @开始触摸 */
function touchStart(event, index) {
    // 只读
    if (props.readonly) return
    // H5拖拽时，禁止触发ios回弹
    h5BodyScroll(false)
    const [{ pageY }] = event.touches

    // 记录数据
    state.currentIndex = index
    state.touchY = pageY
    state.currentItemY = state.itemYList[index]
}

/** @手指滑动 **/
function touchMove(event) {
    // 只读
    if (props.readonly) return
    const [{ pageY }] = event.touches
    const current = state.itemYList[state.currentIndex]
    const prep = state.itemYList[state.currentIndex - 1]
    const next = state.itemYList[state.currentIndex + 1]
    // 获取移动差值
    state.itemYList[state.currentIndex] = {
        top: current.top + (pageY - state.touchY)
    }
    // 记录手指坐标
    state.touchY = pageY
    // 向下移动（超过下一个的1/2就进行换位）
    if (next && current.top > next.top - props.itemHeight / 2) {
        changePosition(state.currentIndex + 1)
    } else if (prep && current.top < prep.top + props.itemHeight / 2) {
        // 向上移动（超过上一个的1/2就进行换位）
        changePosition(state.currentIndex - 1)
    }
    //
}

/** @手指松开 */
function touchEnd() {
    // 只读
    if (props.readonly) return
    // 触发change
    change()
    // 将拖拽的item归位
    state.itemYList[state.currentIndex] = state.initialItemYList[state.currentIndex]
    state.initialList = [...state.newList]
    state.currentIndex = -1
    // H5开启ios回弹
    h5BodyScroll(true)
}

/** @交换位置 **/
// index 需要与第几个下标交换位置
function changePosition(index) {
    // 记录当前拖拽的item数据
    const tempItem = state.newList[state.currentIndex]
    // 设置原来位置的item
    state.newList[state.currentIndex] = state.newList[index]
    // 将临时存放的数据设置好
    state.newList[index] = tempItem

    // 调整位置item
    state.itemYList[index] = state.itemYList[state.currentIndex]
    state.itemYList[state.currentIndex] = state.currentItemY

    // 改变当前操作的的下标
    state.currentIndex = index

    // 记录新位置的数据
    state.currentItemY = state.initialItemYList[state.currentIndex]
}

/** @回传数据 **/
function change() {
    // 如果顺序未发生改变，则不触发change事件
    if (JSON.stringify(state.newList) == JSON.stringify(state.initialList)) return
    // 传给父组件新数据
    emits("change", state.newList, state.newList[state.currentIndex])
}

// h5 ios回弹
function h5BodyScroll(flag) {
    // #ifdef H5
    document.body.style.overflow = flag ? "initial" : "hidden"
    // #endif
}
</script>

<style scoped lang="scss">
.m-drag {
    position: relative;
    // width: 100vw;
    padding: 0 !important;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
    .m-drag-item {
        position: absolute;
        left: 0;
        right: 0;
        transition: all ease 0.25s;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f5f5f5;
        > :deep(*:not(.icon)) {
            flex: 1;
        }
        .icon {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20rpx;
            .lines {
                background: #e0e0e0;
                width: 20px;
                height: 2px;
                border-radius: 100rpx;
                margin-left: auto;
                position: relative;
                display: block;
                transition: all ease 0.25s;
                &::before,
                &::after {
                    position: absolute;
                    width: inherit;
                    height: inherit;
                    border-radius: inherit;
                    background: #e0e0e0;
                    transition: inherit;
                    content: "";
                    display: block;
                }
                &::before {
                    top: -14rpx;
                }
                &::after {
                    bottom: -14rpx;
                }
            }
        }
        // 拖拽中的元素，添加阴影、关闭动画、层级置顶
        &.active {
            box-shadow: 0 0 204rpx rgba(0, 0, 0, 0.08);
            transition: initial;
            z-index: 1;

            // .icon .bars {
            //     background: #a3dbcb;
            // }
        }
    }
}
</style>
