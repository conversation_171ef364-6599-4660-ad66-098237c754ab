<template>
    <view class="container_box">
        <view class="title">基础信息：</view>
        <view class="base_box">
            <view class="item"
                ><text class="l_box">设备名称：</text><text class="r_box">{{ state.info.deviceName }}</text></view
            >
            <view class="item"
                ><text class="l_box">设备类型：</text><text class="r_box">{{ state.info.equipmentTypeName }}</text></view
            >
            <view class="item"
                ><text class="l_box">设备场地：</text><text class="r_box">{{ state.info.siteName }}</text></view
            >
        </view>
        <view class="title">告警信息：</view>
        <view class="base_box">
            <view class="item"
                ><text class="l_box">告警类型：</text><text class="r_box">{{ state.info.alarmTypeName }}</text></view
            >
            <view class="item"
                ><text class="l_box">告警内容：</text><text class="r_box">{{ state.info.content }}</text></view
            >
            <view class="item"
                ><text class="l_box">告警开始时间：</text><text class="r_box">{{ state.info.eventTime }}</text></view
            >
            <view class="item">
                <text class="l_box">告警录音：</text>
                <view class="r_box">
                    <text v-if="!state.info.hasAlarmAudio" style="color: var(--primary-color)">暂无录音</text>
                    <view v-else class="audio_box">
                        <view v-for="item in state.info.alarmAudioUrls" :key="item.id">
                            <text style="color: #ff3145" v-if="item.isExpired">录音已过期</text>
                            <view class="audio_item">
                                <cui-audiobar :durationText="formatDuration(item.duration)" :src="item.fileUrl"></cui-audiobar>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="title">处理信息：</view>
        <view class="base_box" style="margin-bottom: 80px">
            <view class="item"
                ><text class="l_box">处理状态：</text><text class="r_box" :style="{ color: statusColor[state.info.status] }">{{ status[state.info.status] }}</text></view
            >
            <view class="item"
                ><text class="l_box">处理人：</text><text class="r_box">{{ state.info.handlerName }}</text></view
            >
            <view class="item"
                ><text class="l_box">处理时间：</text><text class="r_box">{{ state.info.acceptedAt }}</text></view
            >
            <view class="item"
                ><text class="l_box">处理结束时间：</text><text class="r_box">{{ state.info.finishedAt }}</text></view
            >
            <view class="item">
                <text class="l_box">通话录音：</text>
                <view class="r_box">
                    <text v-if="!state.info.hasCallAudio" style="color: var(--primary-color)">暂无录音</text>
                    <view v-else class="audio_box">
                        <view v-for="item in state.info.callAudioUrls" :key="item.id">
                            <text style="color: #ff3145" v-if="item.isExpired">录音已过期</text>
                            <view class="audio_item">
                                <cui-audiobar :durationText="formatDuration(item.duration)" :src="item.fileUrl"></cui-audiobar>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="item"
                ><text class="l_box">处理结果：</text><text class="r_box">{{ state.info.resultText }}</text></view
            >
            <view class="item">
                <text class="l_box">备注：</text>
                <text style="flex: 1">{{ state.info.remark }}</text></view
            >
        </view>
    </view>
</template>
<script setup>
import { formatDuration } from "@/utils"
import cuiAudiobar from "../components/cui-audiobar/cui-audiobar.vue"

const status = {
    0: "未处理",
    1: "处理中",
    2: "已处理"
}

const statusColor = {
    0: "#FF3145",
    1: "#FFB338",
    2: "var(--primary-color)"
}
const state = reactive({
    info: {
        alarmAudioUrls: [],
        callAudioUrls: []
    }
})

// 获取告警详情
const getDetail = (id) => {
    http.post("/app/anti-bullying/mgmt/alarm/get", { id }).then((res) => {
        if (!res.data) return
        state.info = res.data
    })
}

onLoad((options) => {
    getDetail(options.id)
})
</script>
<style lang="scss" scoped>
.container_box {
    padding: 20rpx 22rpx;
    background: #f7f7f7;
    min-height: calc(100vh - 130rpx);
    .title {
        margin: 24rpx 18rpx;
        font-size: 24rpx;
        color: #4d4d4d;
        font-weight: 600;
    }
    .item {
        display: flex;
        overflow: hidden;
        font-size: 24rpx;
        color: #828282;
        margin-bottom: 20rpx;
        align-items: center;
        .l_box {
            width: 170rpx;
            color: #040404;
        }
        .r_box {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .audio_box {
            display: block;
            width: 336rpx;
            .audio_item {
                margin-bottom: 14rpx;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
    .base_box {
        box-sizing: border-box;
        border-radius: 16rpx;
        background: $uni-bg-color;
        padding: 28rpx 18rpx 8rpx 18rpx;
        // display: flex;
        // flex-direction: column;
        // justify-content: space-between;
    }
}
</style>
