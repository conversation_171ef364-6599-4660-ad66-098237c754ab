<template>
    <view class="month_container_box">
        <view class="top_box">
            <view class="up_box">{{ title }}</view>
            <SelectBtn :list="list" v-model:value="value" @handleClick="selectClick"></SelectBtn>
            <Swiper v-if="state.swiperList.length" v-model:current="current" @change="swiperChange" :list="state.swiperList"> </Swiper>
        </view>
        <view class="msg_box">
            <ListMsgBox :show="false" :list="dataList" @handleDetailClick="handleDetailClick"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>
    </view>
</template>

<script setup>
import Swiper from "./components/swiper.vue"
import ListMsgBox from "../../components/listMsgBox.vue"
import SelectBtn from "../../components/selectBtn.vue"
import useQueryList from "../../hook/useQueryList.js"
import { onReachBottom } from "@dcloudio/uni-app"
import { getMonthSAndE } from "@/utils/getDate"

const { getList, dataList, pageNo, status } = useQueryList()

let title = ref(new Date().getFullYear() + "年" + (new Date().getMonth() + 1) + "月")
const list = ref(getMonthSAndE())
const value = ref(new Date().getMonth() + 1)
let current = ref(0)

const state = reactive({
    typeList: [
        { name: "出入校考勤", value: 0, isCheck: true },
        { name: "事件考勤", value: 1 },
        { name: "课程考勤", value: 2 }
    ],
    swiperList: []
})

function getPageList(params) {
    return http.post("/app/teach/attendance/timetableDatePage", params)
}

// 获取周月统计头部
async function getTimeTableDate() {
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate
    }

    const res = await http.post("/app/teach/attendance/timetableDate", params)
    state.swiperList = res.data

    if (res.data.length) {
        let arg = {
            classesId: state.swiperList[current.value].classesId,
            ...params
        }

        getList(getPageList, arg)
    }
}

getTimeTableDate()

const selectClick = (val) => {
    changTitle()
    dataList.value = []
    pageNo.value = 1
    getTimeTableDate()
}

const swiperChange = (val) => {
    dataList.value = []
    pageNo.value = 1
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate,
        classesId: state.swiperList[current.value].classesId
    }
    getList(getPageList, params)
}

onReachBottom(() => {
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate,
        classesId: state.swiperList[current.value].classesId
    }
    getList(getPageList, params)
})

const changTitle = () => {
    title.value = list.value[value.value - 1].startDate.replace("-", "年").replace("-", "月").substr(0, 8)
}

const handleDetailClick = (val) => {
    const params = {
        navTitle: val.studentName,
        title: title.value,
        classesName: val.classesName,
        params: JSON.stringify({
            attendanceId: val.attendanceId,
            classesId: val.classesId,
            endDate: val.endDate,
            startDate: val.startDate,
            type: val.type,
            userId: val.userId
        })
    }
    navigateTo({
        url: "/apps/attendance/lecturer/abnormalDetail/index",
        query: params
    })
}
</script>

<style lang="scss">
.month_container_box {
    background-color: $uni-bg-color-grey;
    .top_box {
        .up_box {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
            background-color: var(--primary-bg-color);
            border-bottom: 1rpx solid #d9d9d9;
        }
        .mian_box {
            padding: 0 30rpx;
            background: #fff;
            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 100rpx;
                border-bottom: 1rpx solid #d9d9d9;
                .left {
                    text-align: left;
                    width: 170rpx;
                }
                .right {
                    text-align: right;
                    width: 250rpx;
                }
                .text {
                    position: relative;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: var(--primary-color);
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
        }
        .down_box {
            padding: 30rpx;
            background-color: #fff;
            display: flex;
            .total_box {
                width: 160rpx;
                text-align: center;
                font-size: 28rpx;
                position: relative;
                border-radius: 20rpx;
                padding: 26rpx 0rpx;
                background-color: $uni-bg-color-grey;
                margin-right: 15rpx;
                .top {
                    font-size: 40rpx;
                    margin: 10rpx 0 16rpx 0;
                    text-align: center;
                }
            }
        }
    }
    .msg_box {
        background-color: #fff;
        margin-bottom: 20rpx;
    }
}
</style>
