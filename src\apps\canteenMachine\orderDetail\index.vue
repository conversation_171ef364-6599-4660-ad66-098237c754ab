<template>
    <div>
        <z-paging ref="paging" class="container_box">
            <view class="main">
                <view class="top">
                    <view style="display: flex; align-items: center">
                        <view style="color: #333">{{ state.title }}</view>
                    </view>
                    <view :style="{ color: color[state.orderStatus], flexShrink: 0, fontSize: '28rpx' }">{{ typeText[state.orderStatus] }}</view>
                </view>
                <view class="text_ident" :style="{ color: iconClo[state.orderStatus] }" v-if="iconType[state.orderStatus]">
                    {{ iconType[state.orderStatus] }}
                </view>
                <view class="msg_box">
                    <view>金额</view>
                    <view class="r_box">¥{{ state.payAmount }}</view>
                </view>
                <view class="item">
                    <view class="l_box">收款方：</view>
                    <view class="r_box">{{ state.merchantName }}</view>
                </view>
                <view class="item items_start">
                    <view class="l_box">内部订单编号：</view>
                    <view class="r_box">{{ state.orderNo }}</view>
                </view>
                <view class="item items_start" v-if="state.orderStatus != 0 && state.orderStatus != 1 && state.orderStatus != 6">
                    <view class="l_box">交易流水号：</view>
                    <view class="r_box">{{ state.transactionId }}</view>
                </view>
                <view class="item">
                    <view class="l_box">创建时间：</view>
                    <view class="r_box">{{ replaceTime(state.createTime) }}</view>
                </view>
                <view class="item" v-if="state.orderStatus != 0 && state.orderStatus != 1">
                    <view class="l_box">支付时间：</view>
                    <view class="r_box">{{ replaceTime(state.payTime) }}</view>
                </view>
            </view>

            <template #bottom>
                <view class="footer_box">
                    <button type="default" class="btn_box" v-if="state.orderStatus === 2 || state.orderStatus === 5 || state.orderStatus === 6 || state.orderStatus === 7" @click.stop="deleteOrder">删除订单</button>
                    <button type="default" class="btn_box" v-if="state.orderStatus === 3" @click="cancelApply">取消申请</button>
                    <button type="default" class="btn_box" v-if="state.orderStatus === 2 || state.orderStatus === 5" @click="applyRefund">申请退款</button>
                    <button type="default" class="btn_box" v-if="state.orderStatus === 0 || state.orderStatus === 1" @click="cancelOrder">取消订单</button>
                    <button type="default" class="btn_box" v-if="state.orderStatus === 4 || state.orderStatus === 5 || state.orderStatus === 7" @click="openPopup">退款详情</button>
                    <button type="default" class="btn_box primary" v-if="state.orderStatus === 0 || state.orderStatus === 1" @click="handlePayment">立即支付</button>
                </view>
            </template>
        </z-paging>

        <uni-popup ref="popupRef" type="center" background-color="#fff" borderRadius="20rpx 20rpx 20rpx 20rpx">
            <view class="container_popup">
                <view class="top">
                    退款详情
                    <uni-icons class="close_icon" type="closeempty" size="20" @click="close"></uni-icons>
                </view>
                <view class="item">
                    <view class="l_box">退款原因：</view>
                    <view class="r_box">{{ state.refundDetails.refundReason }}</view>
                </view>
                <view class="item">
                    <view class="l_box">退款金额：</view>
                    <view class="r_box">￥{{ state.refundDetails.refundAmount }}</view>
                </view>
                <view class="item">
                    <view class="l_box">退款编号：</view>
                    <view class="r_box">
                        {{ state.refundDetails.refundNo }}
                    </view>
                </view>
                <view class="item">
                    <view class="l_box">申请退款时间：</view>
                    <view class="r_box">{{ state.refundDetails.createTime }}</view>
                </view>
                <view class="item" v-if="state.refundDetails.orderStatus == 4">
                    <view class="l_box">退款到账时间：</view>
                    <view class="r_box">{{ state.refundDetails.refundTime }}</view>
                </view>
                <view class="item" v-if="state.refundDetails.orderStatus == 4">
                    <view class="l_box">退款去向：</view>
                    <view class="r_box">{{ state.refundDetails.refundDestination }}</view>
                </view>
                <view class="item" v-if="state.refundDetails.orderStatus == 5 || state.refundDetails.orderStatus == 2">
                    <view class="l_box">退款失败原因：</view>
                    <view class="r_box">{{ state.refundDetails.refundDestination || state.refundDetails.auditRemarks }} </view>
                </view>
            </view>
            <view class="footer_box" v-if="state.refundDetails.orderStatus != 1">
                <button type="default" class="btn_box" v-if="state.refundDetails.orderStatus === 0" @click="cancelApply">取消申请</button>
                <button type="default" class="btn_box" v-if="state.refundDetails.orderStatus === 2 || state.refundDetails.orderStatus === 4 || state.refundDetails.orderStatus === 5" @click="deleteRefund">删除记录</button>
            </view>
        </uni-popup>
    </div>
</template>
<script setup>
import useStore from "@/store"

const { user } = useStore()
const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

const replaceTime = (val) => {
    if (!val) return "-"
    return val.replaceAll("-", ".")
}

const identityUserId = computed(() => {
    return user?.userInfo?.identityUserId
})

const color = {
    0: "#FF7F00",
    1: "#FF7F00",
    2: "#00D190",
    3: "#00D190",
    4: "#00D190",
    5: "#00D190",
    6: "#999999",
    7: "#999999"
}

const typeText = {
    0: "待支付",
    1: "待支付",
    2: "已支付",
    3: "已支付",
    4: "已支付",
    5: "已支付",
    6: "已关闭",
    7: "已关闭"
}

const iconType = {
    7: "退款成功",
    3: "审核中",
    5: "退款失败"
}

const iconClo = {
    7: "#00D190",
    3: "#FAAD14",
    5: "#FF1C1C"
}

const state = reactive({
    info: {},
    OrderList: [],
    isPay: true,
    refundDetails: {},
    studentId: null
})

// 获取订单详情
const getDetails = (id) => {
    http.post("/campuspay/mobile/general-pay-order/details", { id }).then((res) => {
        Object.assign(state, res.data)
    })
}

onLoad((options) => {
    Object.assign(state, options)
    getDetails(state.id)
})

// 去订单列表
const toOrderRecord = () => {
    navigateTo({
        url: "/apps/canteenMachine/orderCanteen/index",
        query: {
            studentId: state.studentId
        }
    })
}

// 取消申请
const cancelApply = () => {
    http.post("/campuspay/mobile/general-pay-order/cancel/apply", { id: state.id }).then((res) => {
        uni.showToast({
            title: "取消申请成功",
            icon: "success",
            duration: 2000
        })
        toOrderRecord()
    })
}

// 立即支付
const handlePayment = () => {
    const params = {
        studentId: state.studentId,
        recipientDataIds: [state.recipientDataId],
        tradeNo: state.tradeNo,
        merchantId: state.merchantId,
        payAmountSum: state.payAmount,
        infos: state.itemName,
        payEndTime: state.payEndTime,
        orderNo: state.sceneNo,
        title: state.title,
        payType: state.payMethod
    }
    navigateTo({
        url: "/apps/canteenMachine/orderPayment/index",
        query: {
            ...params,
            recipientDataIds: state.recipientDataId,
            studentId: state.studentId
        }
    })
}

// 取消订单
const cancelOrder = () => {
    const params = {
        orderUserId: identityType.value == "eltern" ? state.studentId : identityUserId,
        tradeNo: state.tradeNo
    }
    http.post("/campuspay/mobile/general-pay-order/cancel/order", params).then((res) => {
        uni.showToast({
            title: "取消订单成功",
            icon: "success",
            duration: 2000
        })
        toOrderRecord()
    })
}

// 删除订单
const deleteOrder = () => {
    http.post("/campuspay/mobile/general-pay-order/delete/order", { id: state.id }).then((res) => {
        uni.showToast({
            title: "删除订单成功",
            icon: "success",
            duration: 2000
        })
        toOrderRecord()
    })
}

const popupRef = ref(null)
const openPopup = () => {
    // 获取退款详情
    http.post("/campuspay/mobile/general-pay-order/refund/details", { id: state.id }).then((res) => {
        if (!res.data) {
            uni.showToast({
                title: "退款详情已删除",
                icon: "none"
            })
            return
        }
        state.refundDetails = res.data
        popupRef.value.open()
    })
}
// 关闭弹窗
const close = () => {
    popupRef.value.close()
}

// 删除退款详情
const deleteRefund = () => {
    http.post("/campuspay/mobile/general-pay-order/delete/refund", { id: state.refundDetails.id }).then((res) => {
        uni.showToast({
            title: "删除详情成功"
        })
        close()
        toOrderRecord()
    })
}

const applyRefund = () => {
    navigateTo({
        url: "/apps/canteenMachine/refundRequest/index",
        query: {
            id: state.id,
            studentId: state.studentId
        }
    })
}
</script>
<style scoped lang="scss">
.container_box {
    background: #f9faf9;
    padding-top: 24rpx;

    :deep(.uni-list-item__container) {
        padding: 0;
        background: transparent;
    }

    .main {
        color: #999999;
        background-color: #fff;
        padding: 32rpx;
        overflow-y: auto;
        position: relative;

        .icon_box {
            position: absolute;
            top: 150rpx;
            right: 30rpx;
            width: 160rpx;
            height: 130rpx;
        }

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;
            font-weight: 600;
        }

        .text_ident {
            text-align: right;
            font-weight: 600;
            font-size: 28rpx;
        }

        .msg_box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-size: 28rpx;
            color: #333;
            padding: 24rpx 0;
            border-bottom: 2rpx solid #d5d5d5;
            margin-bottom: 34rpx;

            .r_box {
                overflow: hidden;
                margin-left: 16rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 36rpx;
                font-weight: 600;
            }
        }

        .item {
            display: flex;
            align-items: center;
            margin-bottom: 40rpx;
            justify-content: space-between;
            font-size: 28rpx;

            .l_box {
                flex-shrink: 0;
            }

            .r_box {
                word-break: break-all;
            }
        }
    }

    .items_start {
        align-items: flex-start !important;
    }
}

.container_popup {
    max-width: 690rpx;
    padding: 32rpx 28rpx;
    box-sizing: border-box;

    .top {
        font-size: 34rpx;
        font-weight: 600;
        text-align: center;
        margin-bottom: 32rpx;
        position: relative;

        .close_icon {
            position: absolute;
            right: 0;
            top: 6rpx;
        }
    }

    .item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        justify-content: space-between;
        color: #333;
        font-size: 28rpx;

        .l_box {
            flex-shrink: 0;
            color: #595959;
        }

        .r_box {
            word-break: break-all;
        }
    }
}

.footer_box {
    background-color: #fff;
    text-align: right;
    display: flex;
    align-items: center;
    padding: 26rpx 32rpx 60rpx 32rpx;
    justify-content: flex-end;
    border-top: 2rpx solid #d9d9d9;
    border-radius: 0 0 20rpx 20rpx;

    .btn_box {
        height: 70rpx;
        background: #ffffff;
        border-radius: 36rpx;
        border: 2rpx solid #dfdfdf;
        margin-left: 24rpx;
        margin-right: 0;
        font-size: 28rpx;
        color: #666;
        line-height: 68rpx;

        &:after {
            border: none;
        }
    }

    .primary {
        background: #00d190;
        color: #fff;
        border: none;
    }
}
</style>
