<template>
    <view class="new_build">
        <uni-easyinput class="reset title" type="text" :inputBorder="false" :border="false" :clearable="false" v-model="collectTable.updateSaveForm.title" placeholder="无标题上报表" />
        <uni-easyinput class="reset" type="text" :inputBorder="false" :border="false" :clearable="false" v-model="collectTable.updateSaveForm.description" placeholder="点击添加填写描述" />
        <view class="scloll">
            <TypeList :isDraggable="false">
                <template #after>
                    <button class="add-question" type="primary" plain="true" @click="toggle">
                        <uni-icons type="plusempty" color="var(--primary-color)" size="20"></uni-icons>
                        添加问题
                    </button>
                </template>
            </TypeList>
        </view>
        <button class="add-question" type="primary" style="background-color: var(--primary-color); color: #fff" @click="handlerNext">下一步</button>
        <!-- 普通弹窗 -->
        <uni-popup ref="popup" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
            <view class="popup-content">
                <view class="handle">
                    <view class="handle-title"> 添加问题 </view>
                    <uni-icons class="handle-icon" type="closeempty" size="16" @click="popup.close()"></uni-icons>
                </view>
                <uni-collapse accordion class="content-itme">
                    <uni-collapse-item v-for="item in state.questionList" :key="item.key" :title="item.title" :show-animation="true">
                        <radio-group @change="radioChange($event)">
                            <view class="content" v-for="it in item.children" :key="it.value">
                                <label class="uni-list-cell">
                                    <view>
                                        <radio :value="JSON.stringify(it)" color="var(--primary-color)" />
                                    </view>
                                    <view>{{ it.title || it.typeName }}</view>
                                </label>
                            </view>
                        </radio-group>
                    </uni-collapse-item>
                </uni-collapse>
                <button class="save-btn" type="primary" @click="saveBtn">确定</button>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue"
import TypeList from "./typeList.vue"
import typeForm from "../typeForm.js"
import useStore from "@/store"

const { collectTable } = useStore()

const popup = ref(null)
const state = reactive({
    // 是否启用拖拽组件
    isDraggable: {
        type: Boolean,
        default: true
    },
    formData: {
        name: "",
        names: ""
    },
    questionList: [
        {
            title: "常用问题",
            key: "1",
            value: "",
            type: "common",
            children: []
        },
        {
            title: "自定义问题",
            key: "2",
            value: "",
            type: "custom",
            children: []
        }
    ],
    secleted: {}
})
const toggle = () => {
    popup.value.open("bottom")
}
const saveBtn = () => {
    console.log(state.secleted)
    collectTable.setComponents(state.secleted)
    popup.value.close()
}
const radioChange = (evt) => {
    const { structure, custom, limitCondition, name, typeName, title } = JSON.parse(evt.detail.value)
    if (!name || !typeForm[name]) return
    const params = JSON.parse(JSON.stringify(typeForm[name]))
    params.id = Date.now().toString(36)
    params.typeName = typeName
    if (name === "SelectInput" || name === "MultipleSelect") {
        params.props.options = ["", ""]
    }
    // 常用
    if (custom) {
        params.limitCondition = limitCondition
        params.custom = custom
        params.title = title
    } else {
        // 自定义
        params.title = ""
    }
    // 性别 默认显示选项
    if (structure) {
        let newStructure = JSON.parse(structure)
        params.props.options = newStructure.props.options
    }
    state.secleted = params
}
// 常用问题
const postSampleInfo = () => {
    const params = {
        businessType: "collectTable"
    }
    http.post("/cloud/componentDesigner/sample", params).then(({ data }) => {
        state.questionList[0].children = data
    })
}

// 自定义问题
const postCustomInfo = () => {
    const params = {
        businessType: "collectTable"
    }
    http.post("/cloud/componentDesigner/custom", params).then(({ data }) => {
        state.questionList[1].children = data
    })
}
const handlerNext = () => {
    if (!collectTable.updateSaveForm.title) {
        uni.showToast({ title: "标题不能为空！", icon: "none" })
        return
    }

    if (!collectTable.updateComponents.length) {
        uni.showToast({ title: "问题不能为空！", icon: "none" })
        return
    }
    let tipTitle = ""
    const isTitle = collectTable.updateComponents.every((v, indx) => {
        if (!v.title) {
            tipTitle = `题目${indx + 1}标题不能为空！`
            return false
        }
        if (v.name === "SelectInput" || v.name === "MultipleSelect") {
            return v.props.options.every((k, idx) => {
                if (!k) {
                    tipTitle = `题目${indx + 1}选项${idx + 1}不能为空！`
                    return false
                }
                return true
            })
        }
        return true
    })
    if (isTitle) {
        navigateTo({
            url: "/apps/collectTable/setting/index"
        })
    } else {
        uni.showToast({ title: tipTitle, icon: "none" })
    }
}
onShow(() => {
    postSampleInfo()
    postCustomInfo()
})
onMounted(() => {
    //  #ifdef MP-WEIXIN
    postSampleInfo()
    postCustomInfo()
    // #endif
})
</script>
<style scoped lang="scss">
.new_build {
    background-color: #f6f6f6;
    padding-top: 100rpx;

    .scloll {
        height: calc(100vh - 480rpx);
        /* #ifdef MP-WEIXIN */
        height: calc(100vh - 558rpx);
        /* #endif */
        overflow: auto;
    }

    .reset {
        background-color: #fff;
        height: 80rpx;
        text-indent: 20rpx;

        &.title {
            :deep(.uni-easyinput__placeholder-class) {
                font-size: 36rpx;
            }
        }
    }

    .add-question {
        color: var(--primary-color);
        border: 1rpx solid var(--primary-color);
        margin: 20rpx;
    }

    .popup-content {
        .handle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx;

            .handle-title {
                font-size: 40rpx;
                font-weight: 500;
                text-align: center;
                flex: 1;
            }

            .handle-icon {
                width: 40rpx;
            }
        }

        .content {
            .uni-list-cell {
                display: flex;
                align-items: center;
                padding: 20rpx;
                border-bottom: 1rpx solid #eee;
            }
        }

        .save-btn {
            margin: 20rpx;
            background-color: var(--primary-color);
        }
    }
}
</style>
