<template>
    <view class="home_container_box">
        <lgd-tab class="yd_tab" @change="tabChangeFn" v-model="state.tabKey" :tabValue="state.tabList" underlineColor="var(--primary-color)" text-color="var(--primary-color)" />
        <Day v-if="componentId === 'Day'"></Day>
        <Weeks v-if="componentId === 'Weeks'"></Weeks>
        <Month v-if="componentId === 'Month'"></Month>
    </view>
</template>

<script setup>
import lgdTab from "@/subModules/components/lgd-tab/components/lgd-tab/lgd-tab.vue"
import Day from "./day/index"
import Month from "./month/index"
import Weeks from "./weeks/index"
onReachBottom(() => {})

const state = reactive({
    tabKey: 0,
    tabList: ["日", "周", "月"]
})

const comGroup = {
    0: "Day",
    1: "Weeks",
    2: "Month"
}
let componentId = ref("Day")

const tabChangeFn = (num) => {
    componentId.value = comGroup[num]
}
</script>

<style lang="scss">
.home_container_box {
    padding-top: 74rpx;
    position: relative;
    .yd_tab {
        position: fixed;
        top: 74rpx;
        width: 100%;
        z-index: 9999;
        background-color: red;
    }
}
</style>
