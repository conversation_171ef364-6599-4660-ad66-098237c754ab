<template>
    <view class="yd_select_box">
        <scroll-view class="scroll_view" scroll-x="true">
            <view :class="['yd_select', { active_class: isCheck(item.value) }]" v-for="(item, index) in list" :key="index" @click="handleClick(item, index)">
                <view class="item">{{ item.name }}</view>
            </view>
        </scroll-view>
    </view>
</template>
<script setup>
import { computed } from "vue"
const prop = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    multiple: {
        type: Boolean,
        default: false
    },
    value: {
        type: [String, <PERSON>rra<PERSON>, <PERSON><PERSON><PERSON>, Number],
        default: ""
    }
})

const emit = defineEmits(["update:value", "handleClick"])

const isCheck = computed(() => (val) => {
    if (typeof prop.value === "object") {
        return prop.value.includes(val)
    } else {
        return prop.value == val
    }
})

const toEmpty = () => {
    prop.list.forEach((i) => (i.checked = false))
}

const getChecked = () => {
    return prop.list.filter((i) => i.checked).map((i) => i.value)
}

const handleClick = (item) => {
    if (prop.multiple) {
        item.checked = !item.checked
        emit("update:value", getChecked())
    } else {
        toEmpty()
        item.checked = !item.checked
        emit("update:value", item.value)
    }
    emit("handleClick", item)
}
</script>
<style lang="scss" scoped>
.yd_select_box {
    background-color: var(--primary-bg-color);
    color: #333333;
    .scroll_view {
        white-space: nowrap;
    }

    .yd_select {
        display: inline-block;
        cursor: pointer;
        margin: 32rpx 0;
        margin-left: 32rpx;
        &:last-of-type {
            margin-right: 32rpx;
        }
        .item {
            font-size: 28rpx;
            text-align: center;
            padding: 10rpx 32rpx;
            background-color: #fff;
            border-radius: 28rpx;
        }
    }

    .active_class {
        color: #ffffff;
        .item {
            background-color: var(--primary-color);
        }
    }
}
</style>
