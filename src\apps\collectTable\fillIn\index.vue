<template>
    <view class="fillIn">
        <z-paging ref="paging" v-model="state.fillInCards" @query="initPage" :auto="false">
            <view class="tabs">
                <view class="tabs-item" @click="clickTabs(item.key)" :class="{ active: state.active == item.key }" v-for="item in fillInTbs" :key="item.key">{{ item.title }}</view>
            </view>
            <view class="content">
                <view class="van_list_item" ref="scrollview" v-for="(item, idx) in state.fillInCards" :key="item.id" @click="clickItem(item)">
                    <image class="list_item_image" :src="`https://file.1d1j.cn/cloud-mobile/components/${item.alreadyDid ? 'submitted' : 'noReported'}.png`"> </image>
                    <view class="list_item_content">
                        <view class="list_item_title">{{ item.title }}</view>
                        <view class="list_item_title tips">{{ item.description }}</view>
                        <view class="list_item_title tips">发起人：{{ item.creator }}</view>
                    </view>
                    <uni-icons class="list_item_icon" type="right" size="16"></uni-icons>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { ref, reactive, watch } from "vue"
const paging = ref(null)
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    childrenId: {
        type: String,
        default: ""
    }
})

const fillInTbs = [
    { title: "全部", key: "all" },
    { title: "已上报", key: "reported" },
    { title: "未上报", key: "notReported" }
]
const state = reactive({
    active: "all",
    fillInCards: []
})

const clickTabs = (key) => {
    state.active = key
    paging.value?.reload()
}
const clickItem = (item) => {
    navigateTo({
        url: "/apps/collectTable/fillIn/createForm",
        query: {
            id: item.id,
            isEdit: item.alreadyDid,
            childrenId: props.childrenId
        }
    })
}
// 获取统计列表
const initPage = (pageNo, pageSize) => {
    let api = "/app/collectTableWrite/all"
    const params = { pageNo, pageSize, status: null, childrenId: props.childrenId }
    if (state.active === "reported") {
        api = "/app/collectTableWrite/reported"
        params.status = 1
    } else if (state.active === "notReported") {
        api = "/app/collectTableWrite/notReported"
        params.status = 0
    }
    http.post(api, params)
        .then(({ data }) => {
            paging.value?.complete(data.list || false)
        })
        .finally(() => {})
}
watch(
    () => props.childrenId,
    async (val, olVal) => {
        if (val !== olVal) {
            // #ifdef MP-WEIXIN
            await initPage(1, 10)
            // #endif
            paging.value?.reload()
        }
    }
)
onMounted(async () => {
    // #ifdef MP-WEIXIN
    await initPage(1, 10)
    // #endif
})
onLoad((item) => {
    Object.keys(item).forEach((key) => {
        item[key] = decodeURIComponent(item[key])
    })
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>
<style scoped lang="scss">
.fillIn {
    background-color: #f6f6f6;
    height: 100vh;

    &.acitve {
        .z-paging-content-fixed {
            top: 255rpx;
        }

        // #ifdef MP-WEIXIN
        .tabs {
            padding-top: 344rpx;
        }

        // #endif
    }

    .z-paging-content-fixed {
        top: 180rpx;
    }

    .tabs {
        display: flex;
        justify-content: center;
        align-items: center;
        // #ifdef MP-WEIXIN
        z-index: 9;
        padding-top: 270rpx;

        // #endif
        .tabs-item {
            margin: 20rpx 0;
            padding: 15rpx 70rpx;
            background-color: #fff;

            &:first-child {
                border-radius: 30rpx 0 0 30rpx;
            }

            &:last-child {
                border-radius: 0 30rpx 30rpx 0;
            }

            &.active {
                background-color: var(--primary-color);
                color: #fff;
            }
        }
    }

    .van_list_item {
        background: #fff;
        display: flex;
        padding: 20rpx 40rpx;
        margin: 20rpx 40rpx;
        border-radius: 30rpx;

        .list_item_image {
            margin-right: 20rpx;
            width: 70rpx;
            height: 70rpx;
        }

        .list_item_content {
            flex: 1;

            .tips {
                color: #333333;
                font-weight: 400;
                font-size: 24rpx;
                margin: 10rpx 0;
            }
        }

        .list_item_icon {
            line-height: 120rpx;
        }
    }

    .empty {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

// }
</style>
