<template>
    <view>
        <uni-nav-bar left-icon="left" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="top_nav_bar">{{ data.navTitle }}</view>
        </uni-nav-bar>
        <view class="information_container_box">
            <lgd-tab class="yd_tab" @change="tabChangeFn" v-model="data.tabKey" :tabValue="data.tabList" underlineColor="#11C685" text-color="#11C685" />
            <view class="top_box">
                <view class="up_box">{{ data.title }}</view>
                <SelectBtn :list="list" v-model:value="value" @handleClick="selectClick"></SelectBtn>
            </view>
            <view class="msg_box">
                <ListMsgBox :list="dataList" @handleDetailClick="handleDetailClick"></ListMsgBox>
                <uni-load-more iconType="auto" :status="status" />
            </view>
        </view>
    </view>
</template>

<script setup>
import lgdTab from "@/subModules/components/lgd-tab/components/lgd-tab/lgd-tab.vue"
import ListMsgBox from "../../components/listMsgBox.vue"
import SelectBtn from "../../components/selectBtn.vue"
import useQueryList from "../../hook/useQueryList.js"
import { onReachBottom } from "@dcloudio/uni-app"
import { getMonthWeekList, getWeekOfMonth, getMonthSAndE } from "@/utils/getDate"

const { weekOfMonth, startOfWeek, endOfWeek } = getWeekOfMonth()
const { getList, dataList, pageNo, status } = useQueryList()

const list = ref([])
const value = ref(weekOfMonth)

let data = reactive({
    navTitle: "",
    title: "",
    value: "0",
    tabKey: 0,
    tabList: ["周", "月"],
    typeData: "",
    checkData: ""
})

onLoad((params) => {
    // 区分周考勤和月考勤
    if (params.type === "0") {
        list.value = getMonthWeekList()
    } else {
        list.value = getMonthSAndE()
    }
    data.navTitle = params.navTitle
    data.checkData = params.checkData
    data.typeData = params.typeData
    data.title = params.title
    data.tabKey = +params.type
    data.classesId = params.classesId
    value.value = params.value
    getTableList()
})

// 首次进入避免触发
let count = 0
const tabChangeFn = (num) => {
    pageNo.value = 1
    dataList.value = []
    if (count) {
        value.value = 1
        if (num === 0) {
            list.value = getMonthWeekList()
        } else {
            list.value = getMonthSAndE()
        }
        changTitle(list.value[0], num)

        getTableList()
    }
    count++
}

function capitalize(date) {
    return date.replace("-", "年").replace("-", "月") + "日"
}

const selectClick = (val) => {
    pageNo.value = 1
    dataList.value = []
    changTitle(val, data.tabKey)
    getTableList()
}

const back = () => {
    uni.navigateBack(1)
}

const handleDetailClick = (val) => {
    const params = {
        navTitle: val.studentName,
        title: data.title,
        params: JSON.stringify({
            attendanceId: val.attendanceId,
            classesId: val.classesId,
            endDate: val.endDate,
            startDate: val.startDate,
            type: val.type,
            userId: val.userId
        })
    }
    navigateTo({
        url: "/apps/attendance/classTeacher/abnormalDetail/index",
        query: params
    })
}

function changTitle(val, type) {
    if (type === 0) {
        data.title = capitalize(val.startDate) + " - " + capitalize(val.endDate)
    } else {
        data.title = val.startDate.replace("-", "年").replace("-", "月").substr(0, 8)
    }
}

function getPageList(params) {
    return http.post("/app/master/attendance/dateStatisticsPage", params)
}

onReachBottom(() => {
    getTableList()
})

function getTableList() {
    const time = list.value[value.value - 1]
    const params = {
        classesId: state.classesId,
        startDate: time.startDate,
        endDate: time.endDate,
        type: data.typeData,
        attendanceId: data.checkData
    }
    getList(getPageList, params)
}
</script>

<style lang="scss">
.top_nav_bar {
    flex: 1;
    text-align: center;
    line-height: 88rpx;
    font-size: 32rpx;
    font-weight: 600;
}
.information_container_box {
    padding-top: 74rpx;
    position: relative;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .yd_tab {
        position: fixed;
        top: 74rpx;
        width: 100%;
        z-index: 9999;
        background-color: red;
    }
    .top_box {
        .up_box {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
            background-color: var(--primary-bg-color);
            border-bottom: 1rpx solid #d9d9d9;
        }
        .down_box {
            padding: 30rpx;
            background-color: #fff;
            display: flex;
            .total_box {
                width: 160rpx;
                text-align: center;
                font-size: 28rpx;
                position: relative;
                border-radius: 20rpx;
                padding: 26rpx 0rpx;
                background-color: $uni-bg-color-grey;
                margin-right: 15rpx;
                .top {
                    font-size: 40rpx;
                    margin: 10rpx 0 16rpx 0;
                    text-align: center;
                }
            }
        }
    }
    .msg_box {
        padding-top: 20rpx;
        background-color: #ffffff;
    }
}
</style>
