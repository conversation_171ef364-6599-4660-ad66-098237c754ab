<template>
    <view class="drag">
        <view class="handle">
            <view class="handle-title"> <text class="order_color"> 排序 </text></view>
            <uni-icons class="handle-icon" type="closeempty" size="16" @click="close"></uni-icons>
        </view>
        <view class="content">
            <l-drag :list="dragList" @change="change" ref="dragRef" after remove :column="1">
                <template #grid="{ active, index, oldindex, oindex, content }">
                    <view class="type_title">
                        <view class="input">
                            <text class="order_color">
                                {{ orderNumber(index) + "." }}
                            </text>
                            <text class="van-field">{{ content.title }}</text>
                        </view>
                    </view>
                </template>
            </l-drag>
        </view>
        <view class="footer">
            <button class="button" type="primary" style="background-color: var(--primary-color)" @click="save">确定</button>
        </view>
    </view>
</template>

<script setup>
import lDrag from "./components/lime-drag/components/l-drag/l-drag.vue"
const emit = defineEmits(["close", "updateList"])
const props = defineProps({
    dragList: {
        type: Array,
        default: () => []
    }
})
const _dragList = ref([])
// 索引的样式
const orderNumber = (order) => {
    let num = order + 1
    return `${num > 9 ? num : "0" + num}`
}
// 改变后的数据
const change = (v) => {
    _dragList.value = v
}
// 关闭弹框
const close = () => {
    emit("close")
}
// 保存
const save = () => {
    emit("updateList", _dragList.value)
    emit("close")
}
</script>

<style lang="scss" scoped>
@import "../style.scss";
@mixin justify-space-between {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
}

@mixin padding18($size: 28rpx) {
    padding: $size;
}
.drag {
    width: 95vw;
    height: 95vh;
    position: relative;
    .handle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        .handle-title {
            font-size: 40rpx;
            font-weight: 500;
            text-align: center;
            flex: 1;
        }
        .handle-icon {
            width: 40rpx;
        }
    }
    .content {
        padding: 0 20rpx;
        height: calc(100% - 230rpx);
        .type_title {
            @include justify-space-between();
            padding-bottom: 17rpx;

            .three-point {
                @include fontSize(30rpx);
                color: $border-color;
            }
        }
    }
    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20rpx;
    }
}
</style>
