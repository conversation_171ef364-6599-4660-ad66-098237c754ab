<template>
    <view class="msg_list_box" v-for="item in dataList" :key="item.id" @click="handleClick(item)">
        <view class="box">
            <view class="top">
                <view style="display: flex; align-items: center">
                    <view style="color: #333">{{ item.title }}</view>
                </view>
                <view :style="{ color: color[item.orderStatus], flexShrink: 0, fontSize: '28rpx' }">{{ typeText[item.orderStatus] }}</view>
            </view>
            <view class="text_ident" :style="{ color: iconClo[item.orderStatus] }" v-if="iconType[item.orderStatus]">
                {{ iconType[item.orderStatus] }}
            </view>
            <view class="msg_box">
                <view>金额</view>
                <view class="r_box">¥{{ item.payAmount }}</view>
            </view>
            <view style="display: flex; justify-content: flex-end">
                <button type="default" class="btn_box" v-if="item.orderStatus === 3" @click.stop="cancelApply(item)">取消申请</button>
                <button type="default" class="btn_box" v-if="item.orderStatus === 0 || item.orderStatus === 1" @click.stop="cancelOrder(item)">取消订单</button>
                <button type="default" class="btn_box" v-if="item.orderStatus === 2 || item.orderStatus === 5 || item.orderStatus === 6 || item.orderStatus === 7" @click.stop="deleteOrder(item)">删除订单</button>
                <button type="default" class="btn_box primary" v-if="item.orderStatus === 0 || item.orderStatus === 1" @click.stop="handlePayment(item)">立即支付</button>
            </view>
        </view>
    </view>
</template>

<script setup>
const prop = defineProps({
    dataList: {
        type: Array,
        default: () => []
    }
})

// 0||1 : 取消订单  支付立即  待支付
// 2:删除订单  已支付
// 3:取消申请  已支付  (审核中)
// 4:已支付 (无按钮)
// 5:删除订单  已支付 (退款失败)
// 6:删除订单 关闭
// 7:删除订单 关闭(退款成功)
// 4||5||7 退款详情

const color = {
    0: "#FF7F00",
    1: "#FF7F00",
    2: "#00D190",
    3: "#00D190",
    4: "#00D190",
    5: "#00D190",
    6: "#999999",
    7: "#999999"
}

const typeText = {
    0: "待支付",
    1: "待支付",
    2: "已支付",
    3: "已支付",
    4: "已支付",
    5: "已支付",
    6: "已关闭",
    7: "已关闭"
}

const iconType = {
    7: "退款成功",
    3: "审核中",
    5: "退款失败"
}

const iconClo = {
    7: "#00D190",
    3: "#FAAD14",
    5: "#FF1C1C"
}

const emit = defineEmits(["handleClick", "handlePayment", "initData", "cancelOrder"])

const handleClick = (item) => {
    emit("handleClick", item)
}

// 取消申请
const cancelApply = (item) => {
    http.post("/campuspay/mobile/general-pay-order/cancel/apply", { id: item.id }).then((res) => {
        uni.showToast({
            title: "取消申请成功",
            icon: "success",
            duration: 2000
        })
        emit("initData", item)
    })
}

// 取消订单
const cancelOrder = (item) => {
    emit("cancelOrder", item)
}

// 删除订单
const deleteOrder = (item) => {
    http.post("/campuspay/mobile/general-pay-order/delete/order", { id: item.id }).then((res) => {
        uni.showToast({
            title: "删除订单成功",
            icon: "none",
            duration: 2000
        })
        emit("initData", item)
    })
}

// 支付操作
const handlePayment = (item) => {
    emit("handlePayment", item)
}
</script>

<style lang="scss" scoped>
.msg_list_box {
    background: #f9faf9;
    padding: 24rpx 30rpx 0 24rpx;
    &:last-of-type {
        padding-bottom: 24rpx;
    }
    .box {
        padding: 32rpx;
        width: 690rpx;
        min-height: 290rpx;
        color: #999999;
        background: #ffffff;
        border-radius: 16rpx;
        box-sizing: border-box;
        position: relative;
        margin: 0 auto;
        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;
            font-weight: 600;
        }
        .text_ident {
            text-align: right;
            font-weight: 600;
            font-size: 28rpx;
        }
        .msg_box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-size: 28rpx;
            color: #333;
            margin: 28rpx 0;
            .r_box {
                overflow: hidden;
                margin-left: 16rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 36rpx;
                font-weight: 600;
            }
        }
        .text_box {
            text-align: left;
            margin-bottom: 32rpx;
            display: flex;
            .l_text {
                flex-shrink: 0;
            }
            .r_text {
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
        .btn_box {
            height: 70rpx;
            background: #ffffff;
            border-radius: 36rpx;
            border: 2rpx solid #dfdfdf;
            margin-left: 24rpx;
            margin-right: 0;
            font-size: 28rpx;
            color: #666;
            line-height: 68rpx;
            &:after {
                border: none;
            }
        }
        .primary {
            background: #00d190;
            color: #fff;
            border: none;
        }
    }
}
</style>
