<template>
    <view class="month_container_box">
        <uni-nav-bar left-icon="left" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()">
            <view class="top_nav_bar">
                <view class="top_text">{{ data.navTitle }}</view>
                <view class="bottom_text">{{ data.classesName }}</view>
            </view>
        </uni-nav-bar>
        <view class="top_box">
            <view class="up_box">{{ data.title }}</view>
            <SelectBtn :list="data.list" v-model:value="data.value" @handleClick="selectClick"></SelectBtn>
        </view>
        <view class="msg_box">
            <ListMsgBox :type="data.params.type" :list="dataList"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>
    </view>
</template>

<script setup>
import ListMsgBox from "./components/listMsgBox.vue"
import SelectBtn from "../../components/selectBtn.vue"
import useQueryList from "../../hook/useQueryList.js"
import { onReachBottom } from "@dcloudio/uni-app"

const { getList, dataList, pageNo, status } = useQueryList()

let originArr = [
    { name: "全部", value: "", type: "" },
    { name: "迟到", value: 2, type: "beLateNum" },
    { name: "早退", value: 3, type: "leaveEarlyNum" },
    { name: "缺勤", value: 1, type: "absenteeismNum" }
]

let data = reactive({
    navTitle: "",
    title: "",
    classesName: "",
    list: [],
    value: "",
    params: {}
})

onLoad(async (params) => {
    data.navTitle = params.navTitle
    data.title = params.title
    data.classesName = params.classesName
    data.params = { ...JSON.parse(params.params), ...{ status: data.value } }
    const { data: detailData } = await getTableList(data.params)
    if (detailData) {
        data.list = originArr.map((i) => {
            return {
                name: i.name + (detailData[i.type] !== undefined ? "(" + detailData[i.type] + ")" : ""),
                value: i.value
            }
        })
    }
})

const back = () => {
    uni.navigateBack(1)
}

function getPageList(params) {
    return http.post("/app/teach/attendance/timetableDatePageDetail", params)
}

function getTableList(params) {
    return new Promise(async (resolve) => {
        const res = await getList(getPageList, params)
        resolve(res)
    })
}

const selectClick = (val) => {
    pageNo.value = 1
    dataList.value = []
    const params = {
        ...data.params,
        ...{ status: data.value }
    }
    getTableList(params)
}

onReachBottom(() => {
    const params = {
        ...data.params,
        ...{ status: data.value }
    }
    getTableList(params)
})
</script>

<style lang="scss">
.month_container_box {
    background-color: $uni-bg-color-grey;
    .top_nav_bar {
        flex: 1;
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
        .bottom_text {
            padding-top: 5rpx;
            font-size: 24rpx;
            color: #999999;
        }
    }
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_box {
        .up_box {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
            background-color: var(--primary-bg-color);
            border-bottom: 1rpx solid #d9d9d9;
        }
        .down_box {
            padding: 30rpx;
            background-color: #fff;
            display: flex;
            .total_box {
                width: 160rpx;
                text-align: center;
                font-size: 28rpx;
                position: relative;
                border-radius: 20rpx;
                padding: 26rpx 0rpx;
                background-color: $uni-bg-color-grey;
                margin-right: 15rpx;
                .top {
                    font-size: 40rpx;
                    margin: 10rpx 0 16rpx 0;
                    text-align: center;
                }
            }
        }
    }
    .msg_box {
        background-color: #fff;
        padding-bottom: 20rpx;
    }
}
</style>
