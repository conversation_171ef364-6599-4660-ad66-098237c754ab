<template>
    <m-drag :item-height="50" :list="list" @change="dragComplete" @emitDelete="deleteComplete">
        <!-- <template #delete="{ item, index }">
            <span style="color: red;" @click="list.splice(index, 1)">删除</span>
        </template> -->
        <template #default="{ item }">
            <span class="name">{{ item.name }}</span>
        </template>
    </m-drag>
    <button @click="addDragComplete">添加item</button>
</template>

<script setup>
import { ref } from "vue"
import MDrag from "./myDrag.vue"
const list = ref([
    {
        name: "余额宝支付"
    },
    {
        name: "余额支付余额支付余额支付余额支付余额支付"
    },
    {
        name: "建设银行储蓄卡支付"
    },
    {
        name: "农业银行储蓄卡支付-农业银行储蓄卡支付-农业银行储蓄卡支付"
    }
])
// 删除完成
function deleteComplete(index) {
    list.value.splice(index, 1)
}
// 拖拽完成
function dragComplete(newList, dragItem) {
    console.log({ newList }, { dragItem })
    list.value = newList
}
function addDragComplete(newList, dragItem) {
    list.value.push({ name: `拖拽item${list.value.length + 1}` })
}
</script>

<style lang="scss" scoped>
.name {
    display: flex;
    align-items: center;
    margin: 0 24rpx;
    // height: 50px;
    color: #383838;
    font-size: 30rpx;
}
</style>
