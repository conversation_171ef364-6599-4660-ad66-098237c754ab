<template>
    <view class="container_box">
        <view class="header_box" @click="toDetail">
            <view class="l">
                <img class="img" :src="state.info.iconUrl" />
                <view class="flag" :style="{ backgroundColor: state.info.level === 'department' ? 'var(--primary-color)' : '#FF992B' }">
                    {{ state.info.level === "department" ? "院" : "校" }}
                </view>
            </view>
            <view class="r">
                <view class="top"> {{ state.info.department }} {{ state.info.name }}</view>
                <view class="main">{{ state.info.description }}</view>
                <view class="btm">
                    <view class="left">
                        <img class="img" :key="idx" v-for="(item, idx) in state.memberList" :src="item.avatarUrl || '@nginx/workbench/groupManage/baseAvatar.png'" />
                    </view>
                    <view>{{ state.info.memberCount }}人</view>
                </view>
            </view>
        </view>
        <view class="item_box" :style="{ filter: !item.isRead ? 'opacity(1)' : 'opacity(0.7)' }" :key="idx" v-for="(item, idx) in state.list" @click="handleClick(item)">
            <view class="top">
                <view class="l">{{ item.title }}</view>
                <view class="r">
                    <text class="drop" v-if="!item.isRead"></text>
                    {{ item.publishedAt && item.publishedAt.replace("T", " ") }}
                </view>
            </view>
            <view class="bottom">{{ item.content }}</view>
        </view>
        <!-- 只有负责人和管理员才显示 -->
        <view class="addNotice" v-if="state.memberRole !== 'member'" @click="toAddNotice">
            <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
        </view>
    </view>
</template>
<script setup>
const state = reactive({
    info: {},
    groupId: "",
    memberList: [],
    list: []
})

onLoad((options) => {
    state.groupId = options.id
    state.memberRole = options.memberRole
    getDetail(options.id)
    getMemberList(options.id)
    getList(options.id)
})

// 社团详情
function getDetail(id) {
    http.post("/app/club/club/get", { id }).then((res) => {
        state.info = res.data
    })
}

// 获取社团成员
function getMemberList(clubId) {
    http.post("/app/club/member/list", { clubId }).then((res) => {
        const arr = res.data || []
        state.memberList = arr.slice(0, 10)
    })
}

function getList(clubId) {
    http.post("/app/club/notice/announcement/list", { clubId }).then((res) => {
        state.list = res.data
    })
}

const handleClick = (item) => {
    navigateTo({
        url: "/apps/clubManage/myGroup/noticeDetail/index",
        query: { id: item.id, clubId: state.groupId, type: "clubManage" },
        success: (res) => {
            // 如果未读跳转成功之后标记已读
            if (!item.isRead) {
                http.post("/app/club/notice/mark-as-read", { ids: [item.id] }).then((res) => {
                    getList(state.groupId)
                })
            }
        }
    })
}

const toAddNotice = () => {
    navigateTo({
        url: "/apps/clubManage/myGroup/addNotice/index",
        query: { clubId: state.info.id }
    })
}

const toDetail = () => {
    navigateTo({
        url: "/apps/clubManage/groupDetail/index",
        query: {
            id: state.info.id
        }
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    padding: 20rpx 30rpx;
    min-height: calc(100vh - 128rpx);
    background-color: $uni-bg-color-grey;
    .header_box {
        margin-bottom: 20rpx;
        height: 220rpx;
        background: $uni-bg-color;
        padding: 30rpx;
        box-sizing: border-box;
        display: flex;
        border-radius: 10rpx;
        .l {
            position: relative;
            width: 160rpx;
            height: 160rpx;
            border-radius: 16rpx;
            overflow: hidden;
            margin-right: 24rpx;
            .img {
                width: 100%;
                height: 100%;
            }
            .flag {
                font-size: 16rpx;
                color: $uni-text-color-inverse;
                position: absolute;
                top: 0;
                width: 24rpx;
                height: 24rpx;
                text-align: center;
                border-radius: 0 0 12rpx 0;
            }
        }
        .r {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            flex: 1;
            overflow: hidden;
            .top {
                font-size: 36rpx;
                color: #262626;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .main {
                font-size: 24rpx;
                color: #262626;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .btm {
                color: #666;
                font-size: 24rpx;
                display: flex;
                align-items: center;
                .left {
                    display: flex;
                    margin-right: 10rpx;
                    .img {
                        width: 36rpx;
                        height: 36rpx;
                        border-radius: 50%;
                        margin-left: -16rpx;
                        &:first-of-type {
                            margin-left: 0;
                        }
                    }
                }
            }
        }
    }
    .item_box {
        padding: 30rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-sizing: border-box;
        margin-bottom: 20rpx;
        .top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30rpx;
            .l {
                font-size: 30rpx;
                font-weight: 600;
                color: #333333;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .r {
                flex-shrink: 0;
                color: #999;
                font-size: 24rpx;
                display: flex;
                align-items: center;
                .drop {
                    display: inline-block;
                    width: 12rpx;
                    height: 12rpx;
                    background-color: #f5222d;
                    border-radius: 50%;
                    margin-right: 8rpx;
                }
            }
        }
        .bottom {
            max-height: 72rpx;
            font-size: 26rpx;
            line-height: 40rpx;
            color: #333;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }
    .addNotice {
        width: 120rpx;
        height: 120rpx;
        background: var(--primary-color);
        position: fixed;
        right: 30rpx;
        bottom: 210rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: -2px 3px 6px 1px #d6f3e8;
    }
}
</style>
