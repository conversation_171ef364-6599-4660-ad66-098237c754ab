<template>
    <div class="album_page">
        <!-- 相册列表 -->
        <z-paging ref="paging" :auto="false" use-virtual-list @query="getAlbumList" v-model="albumList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="校园风采" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
                <!-- tabs切换 -->
                <uv-tabs lineWidth="20" lineColor="var(--primary-color)" :current="tabsCurrent" :scrollable="false" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>

                <!-- 选择班级 -->
                <div v-if="tabsCurrent == 0" class="classes" @click="clickClasses">
                    {{ selectClasses.classesName || "选择班级" }}
                    <image class="sift_icon" src="@nginx/components/siftIcon.png" alt="" />
                </div>
            </template>
            <div class="album_list">
                <div @click="addAlbum" class="item_album" v-if="isTeacher && tabsCurrent == 0 && selectClasses.classesId">
                    <image src="@nginx/workbench/campusStyle/addAlbum.png" alt="" class="album_image" />
                </div>
                <div @click="addAlbum" class="item_album" v-if="isTeacher && tabsCurrent == 1">
                    <image src="@nginx/workbench/campusStyle/addAlbum.png" alt="" class="album_image" />
                </div>
                <div class="item_album" @click="gotoAlbum(item)" v-for="(item, index) in albumList" :key="index">
                    <div class="image_box">
                        <image mode="aspectFill" :src="item.url || '@nginx/workbench/campusStyle/defaultAlbum.png'" alt="" class="album_image" />
                    </div>
                    <div class="album_title">{{ item.name }}</div>
                    <div class="album_num">{{ item.nums || 0 }}张</div>
                </div>
            </div>
            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" v-if="!isTeacher || (tabsCurrent == 0 && !selectClasses.classesId)" />
                </slot>
            </template>
        </z-paging>

        <!-- 选择班级弹框 -->
        <yd-select-popup title="请选择班级" ref="selectPopupRef" :list="classList" :fieldNames="{ value: 'classesId', label: 'classesName' }" @closePopup="closeClasses" :selectId="[selectClasses.classesId]" />
    </div>
</template>

<script setup>
import useStore from "@/store"

const { user } = useStore()
const paging = ref(null)
const appCode = ref(null) // 应用标识
const selectPopupRef = ref(false) // 选择弹框dom
const tabsCurrent = ref(0) // tabs当前索引
const tabsId = ref(1) // tabs当前id
const classList = ref([]) // 班级列表
const albumList = ref([]) // 相册列表
const selectClasses = ref({}) // 当前选中班级
const type = ref(1) // 1为图片 2为视频
const tabsList = [
    { name: "班级", id: 1 },
    { name: "校级", id: 2 }
]

// 是否为教师端（教师端有操作功能）
const isTeacher = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    return !["eltern", "student"].includes(roleCode)
})

// 获取相册列表
async function getAlbumList() {
    await http
        .post("/brand/album/classify/list", {
            kind: tabsId.value,
            classesId: tabsId.value == 1 ? selectClasses.value.classesId : null
        })
        .then((res) => {
            paging.value.setLocalPaging(res.data)
        })
        .catch(() => {
            paging.value.setLocalPaging([])
        })
}

// 初始化获取班级
async function init() {
    try {
        const res = await http.get("/app/roll/getClassesInfo", { code: appCode.value })
        classList.value = res.data
        // 有班级才可以获取相册
        if (res.data?.length > 0) {
            selectClasses.value = res.data[0] || {}
            await getAlbumList()
        } else {
            paging.value.setLocalPaging([])
            uni.showToast({
                title: "暂无班级",
                icon: "none"
            })
        }
    } catch (error) {
        console.log(error, "error")
    }
}

function addAlbum() {
    navigateTo({
        url: "/apps/campusStyle/createAlbum",
        query: {
            kind: tabsId.value,
            classesId: tabsId.value == 1 ? selectClasses.value.classesId : null
        }
    })
}

// tabs切换点击
function tabsClick(item) {
    tabsCurrent.value = item.index
    tabsId.value = item.id
    getAlbumList()
}

// 打开选择班级弹框
function clickClasses() {
    selectPopupRef.value.open()
}

// 取消选择班级弹框
function closeClasses(item) {
    if (item && selectClasses.value.classesId != item.classesId) {
        selectClasses.value = item
        getAlbumList()
    }
}

function gotoAlbum(item) {
    console.log(item)
    navigateTo({
        url: "/apps/campusStyle/albumDetails",
        query: {
            ...item,
            type: type.value
        }
    })
}

onShow(() => {
    init()
})

onLoad((option) => {
    appCode.value = option.code
})
</script>

<style lang="scss" scoped>
.album_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
}
.classes {
    padding: 30rpx;
    background: $uni-bg-color;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-text-color;
    line-height: 40rpx;
    .sift_icon {
        width: 28rpx;
        height: 28rpx;
    }
}

.album_list {
    display: grid;
    /* 设置为4列，每列宽度相等 */
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx; /* 单元格之间的间距 */
    padding: 30rpx;
    margin-top: 20rpx;
    .item_album {
        width: 100%;
        height: 100%;

        .image_box {
            background: $uni-bg-color;
            display: flex;
            width: 330rpx;
            height: 330rpx;
            align-items: center;
            justify-content: center;
            border-radius: 20rpx;
        }
        .album_image {
            width: 330rpx;
            height: 330rpx;
            border-radius: 20rpx;
        }
        .album_title {
            margin-top: 10rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .album_num {
            font-weight: 400;
            font-size: 20rpx;
            color: $uni-text-color-grey;
            line-height: 28rpx;
        }
    }
}
</style>
