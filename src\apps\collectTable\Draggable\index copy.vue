<template>
    <div>
        <l-drag :list="list" @change="change" ref="dragRef" after remove>
            <!-- 每一项插槽 grid 的 content 是您传入的数据 -->
            <template #grid="{ active, index, oldindex, oindex, content }">
                <!-- active 是否为当前拖拽项目 根据自己需要写样式 -->
                <!-- index 排序后列表下标 -->
                <!-- oldindex 排序后列表旧下标 -->
                <!-- oindex 列表原始下标，输入的数据排位不会因为排序而改变 -->
                <view class="remove" @click="onRemove(oindex)"></view>
                <view class="inner" :class="{ active }">
                    <text class="text" :class="{ 'text-active': active }">{{ content }}</text>
                </view>
            </template>
            <template #after>
                <view class="grid">
                    <view class="inner extra" @click="onAdd"> 增加 </view>
                </view>
            </template>
        </l-drag>
    </div>
</template>

<script setup>
import lDrag from "./components/lime-drag/components/l-drag/l-drag.vue"

const dragRef = ref(null)
const list = new Array(7).fill(0).map((v, i) => i)

// 拖拽后新的数据
const newList = ref([])
const change = (v) => (newList.value = v)
const onAdd = () => {
    dragRef.value.push(Math.round(Math.random() * 1000))
}
const onRemove = (oindex) => {
    if (dragRef.value && oindex >= 0) {
        // 记得oindex为数组的原始index
        dragRef.value.remove(oindex)
    }
}
</script>

<style lang="scss" scoped>
</style>