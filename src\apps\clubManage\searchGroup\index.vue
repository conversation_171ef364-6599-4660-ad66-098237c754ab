<template>
    <view class="page">
        <z-paging ref="paging" class="container_box" v-model="dataList" @query="queryList" :auto="false">
            <template #top>
                <view class="header">
                    <uni-nav-bar left-icon="left" :border="false" statusBar fixed :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" @clickLeft="routerBack" title="搜索社团"> </uni-nav-bar>
                </view>
            </template>
            <view class="search_box">
                <input type="text" class="query_input_text" v-model="query.name" placeholder="搜索社团" @input="handleSearch" />
                <view class="right" @click="handleSearch">搜索</view>
            </view>
            <view class="main">
                <view class="item_box" v-for="(item, idx) in dataList" @click="handleClick(item)" :key="idx">
                    <img class="img" :src="item.iconUrl" />
                    <view class="flag" :style="{ backgroundColor: item.level === 'department' ? 'var(--primary-color)' : '#FF992B' }">
                        {{ item.level === "department" ? "院" : "校" }}
                    </view>
                    <view class="r_box">
                        <view class="t">{{ item.name }}</view>
                        <view class="btm">{{ item.memberCount }}人</view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" :isMargin="true" />
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import { debounce } from "@/utils"
const query = reactive({
    name: ""
})
const dataList = ref([])
const paging = ref(null)
const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        name: query.name
    }
    http.post("/app/club/club/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const verifySearchStr = (str) => {
    const v = ["", undefined].includes(str)
    if (v) return false
    return str.replace(/^\s+|\s+$/g, "") != ""
}

const handleSearch = debounce(() => {
    if (!verifySearchStr(query.name)) {
        paging.value?.complete(false)
        return uni.showToast({
            icon: "none",
            title: "请输入社团名称"
        })
    }
    uni.showLoading({
        title: "Loading..."
    })
    paging.value.reload()
    uni.hideLoading()
}, 400)

const handleClick = (item) => {
    navigateTo({
        url: "/apps/clubManage/groupDetail/index",
        query: {
            id: item.id
        }
    })
}
const back = () => {
    uni.navigateBack()
}

const handleClearSearch = () => {
    paging.value.reload()
}

onMounted(() => {
    paging.value?.complete(false)
})
</script>
<style lang="scss" scoped>
.page {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;
}
.container_box {
    .search_box {
        background: $uni-bg-color;
        padding: 20rpx 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .query_input_text {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            font-size: 30rpx;
            background: $uni-bg-color-grey;
            flex: 1;
            margin-right: 10px;
        }
        .right {
            font-size: 30rpx;
            color: var(--primary-color);
        }
    }
    .main {
        padding: 20rpx 30rpx;
        background-color: $uni-text-color-inverse;
        .item_box {
            width: 100%;
            display: flex;
            padding: 20rpx 0;
            .img {
                width: 88rpx;
                height: 88rpx;
                border-radius: 12rpx;
                overflow: hidden;
                margin-right: 16rpx;
            }
            .flag {
                font-size: 16rpx;
                color: $uni-text-color-inverse;
                position: absolute;
                width: 24rpx;
                height: 24rpx;
                text-align: center;
                border-radius: 0.375rem 0 0.375rem 0;
            }
            .r_box {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                flex: 1;
                overflow: hidden;
                .t {
                    font-size: 32rpx;
                    color: #262626;
                    flex: 1;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    font-weight: 600;
                }
                .btm {
                    font-size: 24rpx;
                    color: #999999;
                }
            }
        }
    }
}
</style>
