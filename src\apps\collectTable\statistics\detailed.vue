<!-- detailed 收集明细 -->
<template>
    <z-paging ref="paging" refresher-only @onRefresh="getDetail" :auto="false">
        <template #top>
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="收集明细"
                :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        </template>
        <view class="detailed">
            <view id="echarts">
                <qiun-data-charts canvas2d="true" type="ring" :opts="chartOpts" :chartData="state.chartData" />
            </view>
            <view class="body">
                <view v-for="item in identitys" :key="item.key" :span="state.colSpan">
                    <view class="Count">
                        {{ state.detail[state._params.operation == 0 ? "todayGonnaDoPersonDistribute" :
                        "todayAlreadyDidPersonDistribute"][item.key] }}
                    </view>
                    <view class="name">{{ item.name }}</view>
                    <text class="peg" :style="{ background: item.color }"></text>
                </view>
            </view>

            <uni-table ref="table" :type="false" :loading="state.loading" emptyText="暂无更多数据">
                <uni-tr class="reset-uni-tr" align="left">
                    <uni-th v-for="item in detailedTables" :width="item.width" :key="item.key" align="center">
                        {{ item.title }}
                    </uni-th>
                </uni-tr>
                <uni-tr v-for="it in state.detail.todayGonnaDoPersonList" :key="it.concatMethod">
                    <uni-td :span="state.colSpan" v-for="item in state.detailedTables" :key="item.key">
                        {{ it[item.key] }}
                    </uni-td>
                </uni-tr>
            </uni-table>
            <!-- #ifndef MP-WEIXIN -->
             <!-- 小程序不支持Bolb流.要后端支持。目前跟王总那边确定先隐藏 。测试先记录下来。后期再处理 -->
            <button v-if="state.detail.todayGonnaDoPersonList.length" class="save-btn" type="primary" plain="true"
                style="border-color: var(--primary-color); color: var(--primary-color)" @click="downloadFn">下 载</button>
            <!-- #endif -->
        </view>
        <template #empty>
            <yd-empty text="暂无数据" />
        </template>
    </z-paging>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"

const paging = ref(null)
const detailedTables = [
    { title: "姓名", key: "name", width: 100 },
    { title: "班级/部门", key: "organizationName", width: 120 },
    { title: "手机号码", key: "concatMethod", width: 80 }
]
const identitys = [
    { name: "教职工", key: "employeeCount", color: "#03b776" },
    { name: "家长", key: "elternCount", color: "#0281fe" },
    { name: "学生", key: "studentCount", color: "#22C9A0" }
]

const chartOpts = {
    rotateLock: false,
    color: ["#1890FF", "#03b776", "#22C9A0"],
    padding: [5, 5, 5, 5],
    dataLabel: false,
    enableScroll: false,
    legend: {
        show: false,
        position: "bottom",
        lineHeight: 15
    },
    title: {
        name: "全部",
        fontSize: 16,
        color: "#333333"
    },
    subtitle: {
        name: "",
        fontSize: 16,
        color: "#333333"
    },
    extra: {
        ring: {
            ringWidth: 45,
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: "#FFFFFF"
        }
    },
    series: [
        {
            data: [
                { name: "家长", value: 0, labelShow: false },
                { name: "教职工", value: 0, labelShow: false },
                { name: "学生", value: 0, labelShow: false }
            ]
        }
    ]
}

const state = reactive({
    detailedTables: [
        { title: "姓名", key: "name" },
        { title: "班级/部门", key: "organizationName" },
        { title: "手机号码", key: "concatMethod" }
    ],
    detail: {
        todayGonnaDoPersonDistribute: [], //未提交明细
        todayGonnaDoPersonList: [], // 人员班级
        todayAlreadyDidPersonDistribute: [] //已提交明细
    },
    _params: {},
    dataList: [],
    loading: false
})

const getDetail = () => {
    const { id, time, operation } = state._params
    const params = {
        id,
        time,
        operation
    }
    http.post("/app/collectTableStatistics/statisticsDetail", params)
        .then(({ data }) => {
            const {
                answerStatistics: { todayGonnaDoPersonDistribute, todayGonnaDoPersonList, todayAlreadyDidPersonDistribute, todayAlreadyDidPersonList }
            } = data
            // 人员班级统计
            state.detail.todayGonnaDoPersonList = operation == 0 ? todayGonnaDoPersonList : todayAlreadyDidPersonList
            state.detail.todayGonnaDoPersonDistribute = todayGonnaDoPersonDistribute
            state.detail.todayAlreadyDidPersonDistribute = todayAlreadyDidPersonDistribute
            let { elternCount = 0, employeeCount = 0, studentCount = 0 } = operation == 0 ? todayGonnaDoPersonDistribute : todayAlreadyDidPersonDistribute

            // //未提交明细 0   //已提交明细1
            chartOpts.subtitle.name = elternCount + employeeCount + studentCount
            chartOpts.series[0].data = [
                { name: "家长", value: elternCount, labelShow: false },
                { name: "教职工", value: employeeCount, labelShow: false },
                { name: "学生", value: studentCount, labelShow: false }
            ]
            state.chartData = JSON.parse(JSON.stringify(chartOpts))

            paging.value.complete()
        })
        .catch(() => {
            paging.value.complete(false)
        })
}

function clickLeft() {
    uni.navigateBack()
}
function downloadFn(url) {
    const { id, time, operation } = state._params
    const fileUrl = `/app/collectTableStatistics/${operation == 1 ? "downloadAlreadyDid" : "downloadGonnaDo"}`
    if (["yide-ios-app", "yide-android-app", "Ios"].includes(checkPlatform())) {
        const params = {
            filename: "",
            id,
            time,
            url: fileUrl
        }
        sendAppEvent("downloadFile", params)
        // getAndroidNewVersionFn();
    } else if (checkPlatform() == "wx-miniprogram") {
        uni.showToast({ title: "请点击右上角用浏览器打开下载" })
    } else {
        http.exportInfo(fileUrl, "POST", { id, time }, { responseType: "arraybuffer" }, "收集表明细")
    }
}
onLoad((item) => {
    state._params = item
    getDetail()
})
</script>

<style scoped lang="scss">
@import "../style.scss";

#echarts {
    height: 400rpx;
    background: $white-color;
    position: relative;
}

.detailed {
    @include autoHeight(180rpx);
    overflow: hidden auto;
    padding: 10rpx 0;

    .create_footers {
        padding: 25rpx;

        .download {
            margin-bottom: 40rpx;
        }
    }

    .save-btn {
        margin: 10rpx 20rpx;
    }

    .reset-uni-tr {
        background: var(--primary-bg-color);

        :deep(.uni-table-th-content) {
            color: #333;
        }
    }

    .body {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 20rpx 0;
        background: $white-color;
        margin-bottom: 20rpx;

        .Count {
            color: $heading-color;
            font-size: 36rpx;
            text-align: center;
        }

        .name {
            font-size: 26rpx;
            color: #666666;
            margin: 10rpx auto;
        }

        .peg {
            width: 24rpx;
            height: 8rpx;
            display: block;
            border-radius: 4rpx;
            margin: 0 auto;
        }
    }

    :deep(.uni-table-td) {
        text-align: center !important;
    }
}
</style>
