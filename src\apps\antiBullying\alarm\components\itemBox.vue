<template>
    <div class="item_box" @click="handleClick">
        <div class="title">
            霸凌告警
            <div class="flag" :style="{ background: statusColor[itemInfo.status] }">{{ status[itemInfo.status] }}</div>
        </div>
        <div class="btm_box">
            <div class="item">
                <div class="l_box">设备名称：</div>
                <div class="r_box">{{ itemInfo.deviceName }}</div>
            </div>
            <div class="item">
                <div class="l_box">告警场地：</div>
                <div class="r_box">{{ itemInfo.siteName }}</div>
            </div>
            <div class="item">
                <div class="l_box">告警类型：</div>
                <div class="r_box">{{ itemInfo.alarmTypeName }}</div>
            </div>
            <div class="item">
                <div class="l_box">告警时间：</div>
                <div class="r_box">{{ itemInfo.eventTime }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
const emit = defineEmits(["handleClick"])
const props = defineProps({
    itemInfo: {
        type: Object,
        default: () => {}
    }
})

const status = {
    0: "未处理",
    1: "处理中",
    2: "已处理"
}

const statusColor = {
    0: "#FF3145",
    1: "#FFB338",
    2: "var(--primary-color)"
}

const handleClick = () => {
    emit("handleClick")
}
</script>
<style lang="scss" scoped>
.item_box {
    width: 100%;
    height: 370rpx;
    border-radius: 16rpx;
    background: $uni-bg-color;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    .title {
        padding: 30rpx 0 20rpx 20rpx;
        border-bottom: 1rpx solid #f2f2f2;
        font-family: PingFang-SC, PingFang-SC;
        font-weight: bold;
        font-size: 32rpx;
        position: relative;
        .flag {
            width: 134rpx;
            height: 52rpx;
            border-radius: 0rpx 24rpx 0rpx 24rpx;
            position: absolute;
            right: 0;
            top: 0;
            color: $uni-text-color-inverse;
            font-size: 28rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .btm_box {
        display: flex;
        flex-direction: column;
        padding: 32rpx 20rpx;
        height: calc(100% - 94rpx);
        box-sizing: border-box;
        justify-content: space-around;
        .item {
            display: flex;
            overflow: hidden;
            font-size: 28rpx;
            color: #7f7f7f;
            align-items: center;
            .l_box {
                width: 140rpx;
            }
            .r_box {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
