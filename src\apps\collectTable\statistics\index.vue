<template>
    <view class="statistics" :class="{ acitve: childrenId }">
        <z-paging ref="paging" v-model="state.collectList" @query="initPage" :auto="false">
            <view class="statistics_collect_item" v-for="item in state.collectList" :key="item.id">
                <view class="title">{{ item.title }}</view>
                <view class="time">{{ item.createTime }}</view>
                <view class="yes_no_submit" @click="handerDetailed(item)">
                    <span class="yes_submit">
                        <view class="num">{{ item.todayAlreadyDid }}</view>
                        <view class="staus">已提交</view>
                    </span>
                    <view class="van-hairline--left"></view>
                    <span class="no_submit">
                        <view class="num">{{ item.todayGonnaDo }}</view>
                        <view class="staus">未提交</view>
                    </span>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { onMounted, reactive } from "vue"
const paging = ref(null)
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => { }
    },
    childrenId: {
        type: String,
        default: ""
    }
})

const state = reactive({
    collectList: [],
    childrenId: ''
})

const handerDetailed = (item) => {
    navigateTo({
        url: "/apps/collectTable/statistics/details",
        query: { id: item.id }
    })
}
// 获取统计列表
const initPage = (pageNo, pageSize) => {
    const params = { pageNo, pageSize, studentId: props.childrenId };
    http.post("/app/collectTableStatistics/statisticsList", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || false)
        })
        .finally(() => { })
}
const init = () => {
    // #ifdef MP-WEIXIN
    initPage(1, 10)
    // #endif 
    paging.value?.reload()
}
watch(() => props.childrenId, async (val, olVal) => {
    state.childrenId = val
    if (val && olVal && (olVal !== val)) {
        init()
    }
})
onMounted(() => {
    init()
})
onLoad((item) => {
    state.childrenId = item.childrenId
})
</script>
<style scoped lang="scss">
@import "../style.scss";



.statistics {
    padding: 20rpx 0;
    background-color: #f6f6f6;
    height: 100vh;

    &.acitve {
        .z-paging-content-fixed {
            top: 255rpx;
        }

        .statistics_collect_item {
            &:first-child {
                // #ifdef MP-WEIXIN
                margin-top: 367rpx !important;
                //  #endif
            }
        }
    }

    .z-paging-content-fixed {
        top: 170rpx;
    }

    .statistics_collect_item {
        margin: 20rpx 30rpx;
        background: $white-color;
        padding: 32rpx;
        border-radius: 20rpx;

        &:first-child {
            margin-top: 28rpx;
            // #ifdef MP-WEIXIN
            margin-top: 288rpx;
            //  #endif
        }

        .title {
            color: $gray-background-3ff;
            font-weight: 600;
            @include fontSize(30rpx);
        }

        .time {
            margin: 20rpx 0 40rpx;
            color: $gray-background-6ff;
            @include fontSize(28rpx);
        }

        .van-hairline--left {
            height: 80rpx;
        }

        .yes_no_submit {
            display: flex;
            justify-content: space-around;
            text-align: center;

            .yes_submit {
                .num {
                    color: var(--primary-color);
                }
            }

            .no_submit {
                .num {
                    color: $yellow-color;
                }
            }

            .staus {
                margin: 20rpx auto;
                font-size: 28rpx;
            }
        }
    }
}
</style>
