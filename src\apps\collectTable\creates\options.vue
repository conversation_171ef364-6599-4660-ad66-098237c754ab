<template>
    <view>
        <view v-for="(element, index) in state.draggables">
            <view class="type_options_item" :key="index">
                <view class="input">
                    <uni-icons class="handle-icon" type="minus" size="16" :color="state.draggables.length > 2 ? '#ff0000' : '#666666ff'" @click="deleteOptions(state.draggables, index)"></uni-icons>
                    <uni-easyinput class="reset" type="text" :inputBorder="false" :border="false" v-model="state.draggables[index]" :clearable="false" disableColor="#ff0000" placeholder="请输入" />
                </view>
                <span class="opacity">{{ element }}</span>
                <!-- 可拖动操作 -->
                <uni-icons class="handle-icon" color="#d8d8d8" type="bars" size="16" @click="handleDraggableOpen" />
            </view>
        </view>
        <!-- 拖拽弹框 -->
        <uni-popup ref="draggableOptRef" background-color="#fff" borderRadius="20rpx">
            <PopupDraggables :draggableList="draggableList" @dragSave="dragSave" @close="draggableOptRef.close()" />
        </uni-popup>
    </view>
</template>

<script setup>
import { reactive, watch } from "vue"
import PopupDraggables from "../Draggables/popupDraggables.vue"
import useStore from "@/store"
const { collectTable } = useStore()
const state = reactive({
    ramdId: "",
    draggables: []
})
const props = defineProps({
    Options: {
        type: Array,
        default: () => []
    },
    Index: {
        type: Number,
        default: 0
    }
})

const draggableOptRef = ref()
const draggableList = ref([])
const emit = defineEmits(["emitStatrNewData"])
// 打开排序
const handleDraggableOpen = () => {
    draggableList.value = state.draggables.map((item) => ({ title: item }))
    draggableOptRef.value.open()
}

// 确定提交排序
const dragSave = (list) => {
    draggableOptRef.value.close()
    state.draggables = list.map((item) => item.title)
    collectTable.updateComponents[props.Index].props.options = state.draggables
}
// 删除选项
const deleteOptions = (item, indx) => {
    if (item.length > 2) {
        item.splice(indx, 1)
    }
}
watch(
    () => props.Options,
    (val) => {
        state.draggables = val
    },
    { immediate: true }
)
</script>

<style scoped lang="scss">
@import "../style.scss";

@mixin justify-space-between {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
}

.type_options_item {
    @include justify-space-between();
    padding: 10px 0;
    position: relative;

    .input {
        @include fontSize();
        @include justify-space-between();

        :deep(.van-field) {
            padding: 10px;
        }
    }

    .delete {
        color: $border-color;
        @include fontSize(36px);
    }

    .wap-nav {
        color: #b8b8b8;
        @include fontSize(40px);
    }

    .opacity {
        width: 1px;
        height: 1px;
        opacity: 0;
        margin-right: 20px;
        overflow: hidden;
        z-index: -1;
    }
}
</style>
