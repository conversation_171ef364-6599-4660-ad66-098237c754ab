# 项目功能模块
## 1. 登录模块
+ **中性版和一加壹版**：登录页面样式有两个，一个为中性版的页面，一个为一加壹页面，页面由component两个组件区分，一加壹的组件为 **‘cloudLogin’** ，中性版的为 **‘neutralVersion’** ，两个组件除了页面css样式不同以外，其他功能相同
+ **登录方式**：正常登录，钉钉免登，企业微信免登，公众号学校ID跳转登录进入
	::: info 正常登录
	判断账号是否第一次登录，是否有修改过密码，需要修改过密码才可以登录，否则需要让用户获取验证码修改完密码，才可以去登录，需要选择学校，选择角色，只有在选择完学校和角色后，才能获取到最终用户信息，token，学校id等数据
	:::
	
	::: info 钉钉免登
	需要先判断当前的环境是否在钉钉浏览器中，然后在钉钉应用中跳转链接配置好corpId（企业Id），schoolId（学校ID），跳转进入/login路由，需要根据corpId调用钉钉官方API获取到code，然后使用corpId，schoolId，code调用后端获取token接口，拿到token，然后继续调用正常登录的逻辑，判断账号是否第一次登录，是否有修改过密码，在选学校和选角色中默认选第一个
	:::
	
	::: info 企业微信免登
	判断是否在企业微信环境中，主要和微信环境不同，需要在企业微信应用中配置跳转过来的链接，在链接后面需要拼接路由参数code，和state，state中包含corpId，agentId和schoolId，拿到数据后加密去调用获取token接口即可，然后和钉钉免登相同的调用正常登录逻辑，判断账号是否第一次登录，是否有修改过密码，然后去默认选择学校和选择角色，进入home页面即可
	:::
	
	::: info 企业微信免登
	:::
	
+ **滑块功能**: 每次点击登录按钮都需要触发，钉钉和企业微信免登不触发，本地部署不需要这个功能
+ **忘记密码**：
	- 第一次登录的账号需要修改密码，如果忘记密码，需要获取验证码重新设置密码，初始密码只有在第一次登录的时候有效。
	- 本地部署获取不到验证码，直接设置新密码就行，不需要填写验证码

## 2. 首页模块
+ 由通知公告，课表，今日作业，考勤组成，其考勤组件的接口极其恶心，慎改
+ **通知公告**：写在home页的轮播图，点击后进入通知公告应用
+ **课表**：是一个components组件，文件地址：/components/classscard/index.vue，点击进入课表应用
+ **今日作业**：是一个components组件，文件地址：/components/homework/index.vue，点击进入今日作业应用
+ **考勤**：是一个components组件，文件地址：/components/attendancecard/index.vue，点击进入考勤应用

## 3. 消息模块
+ 由待办，学生留言，通讯录和消息通知组成，注意：待办和消息通知不同，并且有两套，一套老版本，一套现版本，有坑，慎改
+ **待办**：只有老师端才可以看，跳转路由/toDo页面，查看详情中，审批类型的跳转老版本路由：/toDo/oldTodoInfo，访客跳转单独路由/visitorSystem，其他待办跳转/toDo/todoInfo

+ **学生留言**：学生端可以看，老师和家长可以看，但是本地部署都不可看。跳转路由：/leaveWord，在学生留言页面使用Iframe，跳转到VITE_BASE_AppH5的域名，为appH5项目中

+ **通讯录**：学生，老师和家长都可看，跳转路由：/addressBook，为当前组织的树形列表。详情页面的手机号码使用了PhoneOperate组件，有拨打电话，复制手机号的功能，文件路径为：/components/PhoneOperate.vue，文件需要传入text号码，和isShow是否展示号码（有些需求只需要改组件的功能，不需要显示号码），根据checkPlatform()判断了微信环境下的方法不同

+ **消息通知**：一个列表，但是后端给的数据处理了很多逻辑，（有个模块类型moduleType字段：1闸机推送,2考勤,3考勤周报,4请假,5通知公告,6.失物招领,7作业,8人脸采集,9.日程提醒,10会议提醒,11作业发布,13收集表,14审批,16公告,17成绩管理,18活动报名,20场地预约）不同的类型和不同的角色（家长老师大学生）会跳转不同的页面和传参，自己看页面吧，文件路径：/src/view/messageNotice/index.vue

## 4. 个工作台模块
+ **应用列表**：应用列表为前端写死的列表， **workBenchList** 为老师和家长展示的列表， **studentWorkBenchList** 为大学端学生展示的列表，需要添加的时候直接去加一个对象就行，老师家长的列表中有个字段为 **show** ，可以去判断当前为老师或者家长不展示该应用使用，点击应用时触发workbenchFn方法，会把你写死的对象传进去，直接用你写死的对象中的code判断你要跳转的路由，如果是跳转别的项目，需要在本项目加个路由，然后直接用Iframe嵌套你的项目路径即可

+ **应用**：

	+ **考勤应用**: 分为老师端和家长端，路由为：/attendance/teacherData和/attendance/parentData，家长端需要选择自己的孩子，如果有两个孩子，默认选择列表第一个为学生ID

	+ **教师考勤**：只有老师才可以看的应用，跳转路由为/teacherAttendance，页面嵌套的是VITE_BASE_APPLET： **uniapp分包应用** [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) ，需要传过去一个token即可，教师考勤的定位功能暂时没有，因为高德地图的获取经纬度功能需要花钱，所以暂时写死了，本地部署暂时写死为南通市紫琅第一小学的经纬度，云平台那边设置的打卡地址也相同写死了。

	+ **收集表**：家长老师学生都可看，路由跳转/collect，页面使用Iframe嵌套env.VITE_BASE_AppH5： **appH5项目** [项目仓库](https://git.yyide.com/ydyjopen/h5/apph5) [项目文档](../appH5/index.md) 

	+ **课表**：家长老师学生都可看，路由跳转为/course/myCourse，只有查看功能，没有新增编辑删除的功能，需要去云平台增删改

	+ **信息发布**： 家长老师学生都可看，路由跳转/information，类型居多，多种类型多种查看方式，没有新增编辑删除的功能，需要去云平台增删改

	+ **通行**： 家长老师学生都可看，区分了家长和老师页面，老师的为/current/school，家长的为/current/parent，老师可为学生添加放行，可以查看学生的通行记录和放行过的放行记录和老师自己的通行记录，家长只可查看自己小孩的通行记录
	
	+ **今日作业**： 只有家长和老师可看，分为两个路由，老师：/work/teacherData，家长：/work/parentData，只可查看，不可新增编辑删除

	+ **校园视频**：分为班级和校级，所有角色都可查看，老师可以把学生的视频上传到班级中，家长进入后可查看到老师上传的视频，也可上传到校级中，不同班级的家长老师也可查看，分为两个路由：老师：/campusVideo/teacherData，家长：/campusVideo/parentData

	+ **校园风采**： 分为班级和校级，所有角色都可查看，和校园视频相同，但是会多分一个相册分类，老师可以新增完相册分类，然后再去上传学生的图片，分为两个路由老师/campusImage/teacherData，家长：/campusImage/parentData

	+ **场地预约**：家长老师学生都可看，路由跳转为/siteBooking，页面使用Iframe嵌套env.VITE_BASE_AppH5： **appH5项目** [项目仓库](https://git.yyide.com/ydyjopen/h5/apph5) [项目文档](../appH5/index.md) 

	+ **审批**：家长老师学生都可看，路由跳转为/approval，页面使用Iframe嵌套[env.VITE_BASE_OAH5：OAH5项目](https://git.yyide.com/ydyjopen/h5/oa-approve-h5)
	
	+ **访客**：只有老师可查看，路由跳转为/visitorSystem，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 

	+ **班级德育**：家长老师学生都可看，路由跳转为/moralEducation，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 
	
	+ **预警消息**：老师可看，路由跳转为/earlyWarning，该应用暂时只做给本地部署紫琅第一小学使用，只有预警的列表和详情页面

	+ **成绩管理**：家长老师学生都可看，路由跳转为/scoreManage，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 

	+ **活动报名**：家长老师学生都可看，路由跳转为/registration，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 
	
	+ **打卡**：家长老师学生都可看，路由跳转为/task，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) ，老师可以新增任务，查看我的任务，打卡我的任务，学生和家长只可以去打卡任务

	+ **巡逻/巡课**：权限问题使用后端接口所得，需要展示的话去开放平台配置即可，巡逻和巡课跳转的是一个页面只是code不同传参不同，还有就是里面有个扫码功能，嵌套给一加壹APP的时候是去调用一加壹APP的方法，在微信中会去调用微信的扫码，会调用后端接口获取的sdk，后端接口如果报错，有可能是后端白名单IP变了，需要重新找商务找公众号的账号去配置，还有每个学校使用的学校ID不同，需要去一德后台配置好才可以使用。页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 
	

	+ **请假**： 老师和家长学生都可看，老师可以审批请假同意拒绝，家长和大学生可以填写请假表单，现在已经被注释掉了无人使用这个应用，老师端跳转路由：/leave/createTeacherLeave，家长端跳转：/leave/createParentLeave

	+ **投票活动**：老师和家长学生都可看，路由跳转为/vote/schoolsVote， 现在已经被注释掉了web-app端无使用这个应用，页面使用Iframe嵌套env.VITE_BASE_AppH5： **appH5项目** [项目仓库](https://git.yyide.com/ydyjopen/h5/apph5) [项目文档](../appH5/index.md) 

	+ **校易付**： 只有家长和大学生可看，使用于校易付系统中，家长可以开通代扣服务，学生在学校可以去刷脸使用消费。路由跳转为：/xyf，页面使用Iframe嵌套[env. VITE_XYF_API：校易付项目](https://git.yyide.com/ydyjopen/h5/yide-pay-applet)

	+ **缴费系统**: 只有家长和大学生可看，用于缴费系统，和校易付也是配套使用的，。路由跳转为：/paySystem，页面使用Iframe嵌套[env. VITE_XYF_API：校易付项目](https://git.yyide.com/ydyjopen/h5/yide-pay-applet)
	
	+ **社团管理**: 只有大学生可看，给学生提供社团加入，我的社团等功能，路由跳转为：/groupManage，页面使用Iframe嵌套env.VITE_BASE_APPLET： **uniapp分包应用**  [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 
	
	+ **群组消息**：只有在本地部署中的老师家长学生才可以看，和信息发布相同，跳转的是/information路由，也只有查看功能
	
### 4. 个人中心模块

+ **我的信息**：可查看个人信息，头像手机号性别生日邮箱

+ **切换角色**：多个角色的账号可以跳转到登录时选择角色的页面，重新选择角色重新获取用户信息重新登录

+  **安全中心**： 可修改密码，忘记密码，修改手机号，注意：这里面都需要获取验证码，但是在本地部署中，是没有获取验证码功能的，做了一些处理，还有就是如果是测试号，乱写的手机号需要获取验证码，有个万能验证码960260可用

+ **隐私政策**： 使用了Iframe嵌套了已写好的HTML文件，文件链接为：https://file.1d1j.cn/privacyPolicy.html

+ **退出登录**：需要二次确认后退出，清空所有的缓存，退回登录页，钉钉/企业微信中的免登也失效，需要重新登录，或者关闭重新进入
