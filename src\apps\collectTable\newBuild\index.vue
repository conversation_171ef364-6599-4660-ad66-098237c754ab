<template>
    <view class="build" :class="{ acitve: childrenId }">
        <z-paging ref="paging" v-model="state.collectList" @query="initPage" :auto="false">
            <view class="new_build">
                <view class="hand_view" :gutter="0" :width="100">
                    <text class="title">请选择新建分类</text>
                    <view class="reset-select">
                        <uv-drop-down ref="dropDown" sign="dropDown_1" text-active-color="#00b781"
                            :extra-icon="{ name: 'arrow-down-fill', color: '#666', size: '26rpx' }"
                            :extra-active-icon="{ name: 'arrow-up-fill', color: '#00b781', size: '26rpx' }"
                            :custom-style="{ padding: '0 30rpx' }">
                            <uv-drop-down-item name="type" type="2" :label="state.order.label"
                                :value="state.order.value">
                            </uv-drop-down-item>
                        </uv-drop-down>
                        <uv-drop-down-popup sign="dropDown_1" :click-overlay-on-close="true"
                            :currentDropItem="currentDropItem" @clickItem="clickItem"
                            @popupChange="openPopup"></uv-drop-down-popup>
                    </view>
                </view>
                <view class="content">
                    <view class="view-item" v-for="item in state.collectList" :key="item.id">
                        <view class="view-box" :class="{ active: item.type == 'create' }"
                            @click="selestMultipleList(item)">
                            <uni-icons type="plusempty" color="var(--primary-color)" size="30"
                                v-if="item.type == 'create'"></uni-icons>
                            <Templates v-else :typeList="item.components" :isDraggable="true" />
                        </view>
                        <text class="list_item_title">{{ item.name }}</text>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { onMounted, reactive } from "vue"
import Templates from "./templates.vue"
import useStore from "@/store"

const { collectTable } = useStore()
const paging = ref(null)
const dropDown = ref(null)
const props = defineProps({
    activeTab: {
        type: Number,
        default: 2
    },
    isCreate: {
        type: Boolean,
        default: false
    },
    childrenId: {
        type: String,
        default: ""
    }
})

const emit = defineEmits(["update:activeTab", "update:isCreate"])
const state = reactive({
    collectList: [],
    result: [],
    order: {
        label: "",
        value: 1,
        activeIndex: 0,
        color: "$uni-text-color",
        activeColor: "var(--primary-color)",
        child: []
    }
})
function openPopup(e) {
    dropDown.value?.open()
}
const clickItem = (e) => {
    state.order.label = e.label || ""
    state.order.value = e.value || ""
    paging.value?.reload()
}
// 获取当前下拉筛选项
const currentDropItem = computed(() => state.order)

const selestMultipleList = (item) => {
    const { id = null, components } = item
    if (id) {
        let { components, name, id, description } = JSON.parse(JSON.stringify(item))
        collectTable.saveForm.title = name
        collectTable.saveForm.id = id
        collectTable.saveForm.description = description
        collectTable.saveForm.components = components
    } else {
        collectTable._updateComponents([])
    }
    emit("update:isCreate", true)
    emit("update:activeTab", 0)
}

const getClassification = (pageNo, pageSize, isAll) => {
    http.post("/app/collectTableClassification/classification", {}).then(({ data }) => {
        if (data.length) {
            state.order.label = data[0].name
            state.order.value = data[0].id
            state.order.child = data.map((v) => {
                return {
                    label: v.name,
                    value: v.id
                }
            })
        }
    })
}

const initPage = () => {
    const params = { classificationId: state.order.value }
    http.post("/app/collectTableClassification/list", params).then(({ data }) => {
        const list = [
            {
                name: "全新创建",
                type: "create",
                id: 0
            },
            ...data
        ]
        paging.value?.complete(list || false)
    })
}

const init = () => {
    nextTick(() => {
        //  #ifdef MP-WEIXIN
        initPage()
        // #endif
        paging.value?.reload()
    })
    getClassification()
}
watch(() => props.childrenId, async (val, olVal) => {
    if (val !== olVal) {
        init()
    }
})
onMounted(() => {
    //  #ifdef MP-WEIXIN
    init()
    // #endif
})
onLoad(() => {
    init()
})
</script>
<style scoped lang="scss">
.build {
    background-color: #f6f6f6;
    height: 100vh;

    &.acitve {
        .z-paging-content-fixed {
            top: 255rpx;
        }

        .new_build {
            /* #ifdef MP-WEIXIN */
            padding-top: 355rpx !important;
            /* #endif */
        }

    }

    .z-paging-content-fixed {
        top: 170rpx;
    }

    .new_build {
        padding: 20rpx 0;
        /* #ifdef MP-WEIXIN */
        padding-top: 283rpx;

        /* #endif */
        .hand_view {
            height: 100rpx;
            padding: 0 20rpx;
            background-color: $uni-text-color-inverse;
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* #ifdef MP-WEIXIN */
            height: 75rpx;

            /* #endif */
            .title {
                font-size: 28rpx;
                color: $uni-text-color;
            }

            .reset-select {
                :deep(.uv-drop-down) {
                    border: none;
                }
            }
        }

        .content {
            padding: 20rpx;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;

            .view-item {
                margin: 10rpx;
                overflow: hidden;

                .list_item_title {
                    text-align: left;
                    margin: 0;
                    font-weight: 600;
                    font-size: 26rpx;
                    color: $uni-text-color;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .view-box {
                    width: 320rpx;
                    height: 320rpx;
                    overflow: hidden;
                    background-color: $uni-text-color-inverse;
                    box-shadow: 0 0 0.26667rem rgba(0, 0, 0, 0.25);
                    border-radius: 18rpx;
                    margin-bottom: 10rpx;
                    padding: 10rpx;
                    margin: 10rpx 0;
                    box-sizing: border-box;

                    &.active {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                }
            }
        }
    }
}
</style>
