<template>
    <view>
        <view class="type_list" v-for="(content, index) in _collectTable" :key="index">
            <view class="type_title">
                <view class="input">
                    <text class="order_color">
                        {{ orderNumber(index) + "." }}
                    </text>
                    <text v-if="content.custom" class="van-field">{{ content.title }}</text>
                    <uni-easyinput v-else class="van-field" v-model="content.title" type="text" :inputBorder="false"
                        :clearable="false" placeholder="编写标题" />
                </view>
                <uni-icons type="more-filled" color="#999" size="20" @click="clickMore(content, index)"></uni-icons>
            </view>
            <view class="type_options">
                <Options :class="{ active: content.props?.options.length > 2 }" v-if="isShowAdd(content.name)"
                    :randomId="GenNonDuplicateID(24)" :Index="index" :Options="content.props?.options || []"
                    @emitStatrNewData="handerStatrNewData" />

                <uni-list-item v-else-if="['DeptPicker', 'ClassesPicker'].includes(content.name)" showArrow
                    title="自动获取" />

                <text v-else-if="content.name === 'TextInput'" class="reset-input">请输入</text>

                <view v-else-if="content.name === 'DateTime'">
                    <text class="nowrap">
                        <uni-icons type="calendar-filled" color="#b8b8b8" size="20"></uni-icons>
                        填写格式: 年-月-日
                        {{ DateTimeText(content) ? "时:分" : "" }}
                    </text>
                    <view>
                        <checkbox-group @change="handleTimeDivision($event, content)">
                            <checkbox :checked="DateTimeText(content)" :active-background-color="primaryColor"
                                :icon-color="whiteColor" class="scale">时分</checkbox>
                        </checkbox-group>
                    </view>
                </view>

                <view v-else-if="content.name === 'FileUpload'">
                    <checkbox-group @change="() => (content.props.imageOnly = !content.props.imageOnly)">
                        <checkbox :checked="content.props.imageOnly" :active-background-color="primaryColor"
                            :icon-color="whiteColor" class="scale">仅允许上传图片</checkbox>
                    </checkbox-group>
                </view>

                <view v-else-if="content.name === 'Location'">
                    <text class="nowrap">
                        <uni-icons type="plus" color="#b8b8b8" size="20"></uni-icons>
                        定位位置：省-市-区 {{ content.props.automaticPositioning ? "" : "+ 详细地址" }}
                    </text>
                    <checkbox-group
                        @change="() => (content.props.automaticPositioning = !content.props.automaticPositioning)">
                        <checkbox :value="content.props.automaticPositioning"
                            :checked="content.props.automaticPositioning" :activeBackgroundColor="primaryColor"
                            :iconColor="whiteColor" :key="content.id" class="scale">详情地址</checkbox>
                    </checkbox-group>

                    <!-- <checkbox-group @change="() => (content.props.autoLocate = !content.props.autoLocate)">
                        <checkbox v-model:value="content.props.autoLocate" :checked="content.props.autoLocate"
                            :activeBackgroundColor="primaryColor" :iconColor="whiteColor" :key="content.id"
                            class="scale">自动定位当前位置</checkbox>
                    </checkbox-group> -->
                </view>

                <view class="type_options_add" v-if="isShowAdd(content.name)" @click="addOptions(content)">
                    <uni-icons type="plus" :color="primaryColor" size="20"></uni-icons>
                    <text class="add_btn">新增选项</text>
                </view>
            </view>
            <view class="type_required van-hairline--top-bottom">
                <checkbox-group @change="changeGroup($event, content)">
                    <checkbox :checked="content.props?.required || false" :active-background-color="primaryColor"
                        :icon-color="whiteColor" :key="content.id" class="scale">
                        必填</checkbox>
                </checkbox-group>
            </view>
            <view class="type">
                <text>{{ content.typeName }}</text>
                <!-- 可拖动操作   -->

                <uni-icons class="handle-icon" color="#d8d8d8" type="bars" size="20" @click="handleDraggableOpen" />
            </view>
        </view>
        <slot name="after"></slot>
        <!-- 普通弹窗 -->
        <uni-popup ref="popup" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
            <view class="popup-content">
                <view class="handle">
                    <view class="handle-title">
                        <text class="order_color">
                            {{ orderNumber(state.questionIndex) }}
                        </text>
                    </view>
                    <uni-icons class="handle-icon" type="closeempty" size="16" @click="popup.close()"></uni-icons>
                </view>
                <view class="content">
                    <uni-list-item :show-extra-icon="true" :extra-icon="{
                        size: '20',
                        type: 'compose'
                    }" title="复制问题" clickable @click="handleCopyDeleteQuestion('copy')" />
                    <uni-list-item :show-extra-icon="true" :extra-icon="{
                        color: '#ff0000',
                        size: '20',
                        type: 'trash'
                    }" title="删除问题" clickable @click="handleCopyDeleteQuestion('delete')" />
                </view>
            </view>
        </uni-popup>
        <!-- 拖拽弹框 -->
        <uni-popup ref="draggableRef" background-color="#fff" borderRadius="20rpx">
            <PopupDraggables :draggableList="draggableList" @dragSave="dragSave" @close="draggableRef.close()" />
        </uni-popup>
    </view>
</template>

<script setup>
import { computed, reactive, toRaw, isReactive } from "vue"
import Options from "./options.vue"
// import Draggable from "../Draggable/index.vue"
import PopupDraggables from "../Draggables/popupDraggables.vue"
import useStore from "@/store"
const { collectTable } = useStore()
const _collectTable = computed(() => collectTable.updateComponents)
const primaryColor = "var(--primary-color)"
const whiteColor = "#ffffff"
const popup = ref()
const draggableRef = ref()
const draggableList = ref([])

const DateTimeText = computed(() => {
    return (element) => element.props.format == "YYYY-MM-DD HH:mm"
})
const props = defineProps({
    // 是否启用拖拽组件
    isDraggable: {
        type: Boolean,
        default: false
    }
})

const state = reactive({
    draggableObj: {},
    isShowAdd: {},
    isTimeDivision: false,
    showAdds: ["MultipleSelect", "SelectInput"],
    questionIndex: 0,
    questionItem: {}
})
const changeGroup = (evt, item) => {
    item.props.required = !!evt.detail.value.length
}
const handleTimeDivision = (evt, element) => {
    element.props.format = !!evt.detail.value.length ? "YYYY-MM-DD HH:mm" : "YYYY-MM-DD"
}

const handerStatrNewData = (item) => {
    const { Index, newOptions } = item
    _collectTable.value[Index].props.options = newOptions
}
const GenNonDuplicateID = (randomLength) => {
    let idStr = Date.now().toString(36)
    idStr += Math.random().toString(36).substr(3, randomLength)
    return idStr
}
const isShowAdd = computed(() => {
    return (item) => state.showAdds.includes(item)
})
// 三点操作 复制 删除
const clickMore = (item, idx) => {
    state.questionIndex = idx
    state.questionItem = JSON.parse(JSON.stringify(item))
    popup.value.open("bottom")
}
// 复制 删除问题
const handleCopyDeleteQuestion = (type) => {
    collectTable.setDetComponents(type, state.questionIndex, state.questionItem)
    popup.value.close()
}
// 添加选项
const addOptions = (item) => {
    const { props, name } = item
    if (name === "SelectInput" && props.options.length > 19) {
        uni.showToast({ title: "最多1个最多20个选项！", icon: "none" })
        return
    }
    props.options.push("")
}
// 索引的样式
const orderNumber = (order) => {
    let num = order + 1
    return `${num > 9 ? num : "0" + num}`
}
// 排序
function deepToRaw(obj) {
    if (!isReactive(obj)) return obj

    const rawObj = toRaw(obj)

    if (Array.isArray(rawObj)) {
        return rawObj.map((item) => deepToRaw(item))
    } else if (typeof rawObj === "object" && rawObj !== null) {
        return Object.fromEntries(Object.entries(rawObj).map(([key, value]) => [key, deepToRaw(value)]))
    }

    return rawObj
}
// 打开排序
const handleDraggableOpen = () => {
    draggableList.value = _collectTable.value
    draggableRef.value.open()
}
// 确定提交排序
const dragSave = (list) => {
    draggableRef.value.close()
    collectTable._updateComponents(list)
}
</script>

<style scoped lang="scss">
@import "../style.scss";

.nowrap {
    padding-left: 4px;
    color: #b8b8b8;
}

.scale {
    // transform: scale(0.95);
    margin-top: 20rpx;
    font-size: 26rpx;
}

@mixin justify-space-between {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
}

@mixin padding18($size: 28rpx) {
    padding: $size;
}

.type_list {
    background: $white-color;
    margin: 20rpx auto;
    @include padding18(14rpx 28rpx);

    .type_title {
        @include justify-space-between();
        padding-bottom: 17rpx;

        .three-point {
            @include fontSize(30rpx);
            color: $border-color;
        }
    }

    .input {
        @include justify-space-between();
        justify-content: left;

        .van-field {
            @include padding18(10rpx);

            :deep(.uni-easyinput__content-input) {
                @include fontSize(30rpx);
                padding: 0 !important;
            }
        }
    }

    .type_options {
        .active {
            :deep(.delete) {
                color: red !important;
            }
        }

        .reset-input {
            font-size: 26rpx;
            color: #999;
        }

        .type_options_add {
            @include padding18(10rpx 0);
            color: var(--primary-color);
            display: flex;
            align-items: center;

            .add-o {
                @include fontSize(36rpx);
                margin-right: 8rpx;
            }

            .add_btn {
                margin-left: 4rpx;
                @include fontSize(30rpx);
            }
        }
    }

    .type_required {
        @include fontSize();
        @include padding18(28rpx 0);
    }

    .wap-nav {
        @include fontSize(40rpx);
        color: #b8b8b8;
    }

    .type {
        @include justify-space-between();
        @include padding18(18rpx 0);
        @include fontSize(26rpx);
    }
}

.popup-content {
    .handle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;

        .handle-title {
            font-size: 40rpx;
            font-weight: 500;
            text-align: center;
            flex: 1;
        }

        .handle-icon {
            width: 40rpx;
        }
    }

    .content {
        .uni-list-cell {
            display: flex;
            align-items: center;
            padding: 20rpx;
            border-bottom: 1rpx solid #eee;
        }
    }
}
</style>
