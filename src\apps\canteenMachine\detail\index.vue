<template>
    <z-paging ref="paging" class="container_box">
        <view class="meal_section">
            <view class="meal_header">
                <text class="meal_title">{{ state.info.mealSetVersionName }}</text>
            </view>
            <view class="dish_list">
                <view class="dish_item" v-for="item in state.info.dishList" :key="item.dishId">
                    <image class="dish_image" :src="item.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                    <view class="dish_content">
                        <text class="dish_name">{{ item.dishName }}</text>
                        <text class="dish_price">¥{{ item.dishPrice }}</text>
                    </view>
                </view>
            </view>
            <view class="extra_section">
                <text class="extra_title">自选额外菜品:</text>
                <view class="dish_list">
                    <view class="dish_item" v-for="item in state.info.singleDishList" :key="item.dishId">
                        <image class="dish_image" :src="item.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                        <view class="dish_content">
                            <text class="dish_name">{{ item.dishName }}</text>
                            <view class="dish_price">¥{{ item.dishPrice }}</view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="total_price">
                <text :style="{ color: state.info.isServing == 0 ? '#F5222D' : 'var(--primary-color)' }">
                    <template v-if="state.info.isServing !== null">
                        <text v-if="state.info.isServing == 1">{{ orderDate }} {{ state.info.servingTime.replace("T", " ") }}</text>
                        <text>{{ state.info.isServing == 0 ? "未就餐" : "已就餐" }}</text>
                    </template>
                </text>
                <text>合计：{{ toFixed(state.info.totalPrice) }}元</text>
            </view>
        </view>
    </z-paging>
</template>
<script setup>
const paging = ref(null)

const state = reactive({
    id: "",
    info: {},
    studentId: null
})

function toFixed(num) {
    if (num == null || num == "") return "0.00"
    return parseFloat(num).toFixed(2)
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.studentId = options.studentId
    state.id = options.id
    http.get("/app/canteen/canteenStudentOrder/get", { id: options.id }).then((res) => {
        console.log("res:", res)
        state.info = res.data
    })
})
</script>
<style lang="scss" scoped>
.container_box {
    background: #f9f9f9;
    :deep(.uv-badge--error) {
        background: var(--primary-color);
        font-weight: normal;
    }
    :deep(.uni-scroll-view) {
        position: static;
    }
    .not_set {
        height: calc(100vh - 502rpx);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .image {
            width: 360rpx;
            height: 210rpx;
        }

        .text {
            font-size: 26rpx;
            font-weight: 400;
            color: #8c8c8c;
            line-height: 36rpx;
            padding-top: 30rpx;
        }
    }
    .meal_section {
        margin-top: 24rpx;
        background-color: #ffffff;
        color: #333;
        border-radius: 16rpx;
        .circle {
            width: 36rpx;
            height: 36rpx;
            border-radius: 50%;
            border: 2rpx solid #b3b3b3;
            margin: 8rpx;
            flex-shrink: 0;
        }

        .meal_header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24rpx 32rpx;

            .meal_title {
                font-size: 16px;
                color: #333333;
                font-weight: 500;
            }
        }
        .dish_list {
            padding: 0 24rpx 0 32rpx;

            .dish_item {
                display: flex;
                align-items: center;
                padding: 20rpx 0;

                .dish_image {
                    width: 140rpx;
                    height: 140rpx;
                    border-radius: 8rpx;
                    margin-right: 24rpx;
                }

                .dish_content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 140rpx;

                    .dish_name {
                        font-size: 14px;
                        color: #333333;
                        line-height: 1.4;
                    }

                    .dish_price {
                        font-size: 14px;
                        color: #999;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                    }
                }
            }
        }
        .extra_section {
            padding-top: 20rpx;

            .extra_title {
                font-size: 14px;
                color: #999999;
                margin-bottom: 20rpx;
                padding: 0 24rpx 0 32rpx;
            }
        }
        .total_price {
            display: flex;
            justify-content: space-between;
            padding: 30rpx 24rpx 30rpx 32rpx;
            font-size: 28rpx;
            color: #666666;
            margin-top: 20rpx;
            border-top: 2rpx solid #d9d9d9;
        }
    }
    .absoult_box {
        position: absolute;
        bottom: 0px;
        left: 10px;
        width: calc(100% - 20px);
        margin-bottom: 0px;
        z-index: 999;
    }
    .poiner_none {
        pointer-events: none;
        background: #f3f3f3;
    }
}
</style>
