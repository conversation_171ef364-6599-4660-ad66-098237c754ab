<template>
    <z-paging ref="paging" class="container_box">
        <view class="main">
            <view class="top">
                <view style="display: flex; align-items: center">
                    <view style="color: #333">{{ state.title }}</view>
                </view>
                <view :style="{ color: color[state.orderStatus], flexShrink: 0, fontSize: '28rpx' }">{{ typeText[state.orderStatus] }}</view>
            </view>
            <view class="text_ident" :style="{ color: iconClo[state.orderStatus] }" v-if="iconType[state.orderStatus]">
                {{ iconType[state.orderStatus] }}
            </view>
            <view class="msg_box">
                <view>金额</view>
                <view class="r_box">¥{{ state.payAmount }}</view>
            </view>
            <view class="item">
                <view class="l_box">收款方：</view>
                <view class="r_box">{{ state.merchantName }}</view>
            </view>
            <view class="item items_start">
                <view class="l_box">内部订单编号：</view>
                <view class="r_box">{{ state.orderNo }}</view>
            </view>
            <view class="item items_start">
                <view class="l_box">交易流水号：</view>
                <view class="r_box">{{ state.transactionId }}</view>
            </view>
            <view class="item">
                <view class="l_box">创建时间：</view>
                <view class="r_box">{{ replaceTime(state.createTime) }}</view>
            </view>
            <view class="item">
                <view class="l_box">支付时间：</view>
                <view class="r_box">{{ replaceTime(state.payTime) }}</view>
            </view>
            <view class="refund">
                <uni-forms ref="valiForm" :rules="state.rules" :modelValue="state" label-position="top">
                    <uni-forms-item label="退款原因" required name="refundReason" class="textarea_box">
                        <uni-easyinput type="textarea" v-model="state.refundReason" :maxlength="200" placeholder="请输入退款原因" primaryColor="var(--primary-color)" />
                        <view class="count_box">{{ state.refundReason.length }}/200</view>
                    </uni-forms-item>
                </uni-forms>
            </view>
        </view>
        <template #bottom>
            <view class="footer_box">
                <button type="default" class="btn_box" @click="onSubmit">确定</button>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
import useStore from "@/store"

const { user } = useStore()
const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

const identityUserId = computed(() => {
    return user?.userInfo?.identityUserId
})
const color = {
    0: "#FF7F00",
    1: "#FF7F00",
    2: "#00D190",
    3: "#00D190",
    4: "#00D190",
    5: "#00D190",
    6: "#999999",
    7: "#999999"
}

const iconType = {
    7: "退款成功",
    3: "审核中",
    5: "退款失败"
}

const iconClo = {
    7: "#00D190",
    3: "#FAAD14",
    5: "#FF1C1C"
}

const typeText = {
    0: "待支付",
    1: "待支付",
    2: "已支付",
    3: "已支付",
    4: "已支付",
    5: "已支付",
    6: "已关闭",
    7: "已关闭"
}

const state = reactive({
    refundReason: "",
    rules: {
        refundReason: {
            rules: [
                {
                    required: true,
                    errorMessage: "请输入退款原因"
                }
            ]
        }
    },
    studentId: null
})

const replaceTime = (val) => {
    if (!val) return "-"
    return val.replaceAll("-", ".")
}

// 获取订单详情
const getDetails = (id) => {
    http.post("/campuspay/mobile/general-pay-order/details", { id }).then((res) => {
        Object.assign(state, res.data)
    })
}

onLoad((options) => {
    Object.assign(state, options)
    getDetails(state.id)
})

const valiForm = ref(null)
const onSubmit = () => {
    valiForm.value.validate().then((res) => {
        const params = {
            orderUserId: identityType.value == "eltern" ? state.studentId : identityUserId,
            orderNo: state.orderNo,
            refundReason: state.refundReason
        }
        http.post("/campuspay/mobile/general-pay-order/applyRefund", params).then(() => {
            uni.showToast({
                title: "申请退款成功",
                icon: "success",
                duration: 2000
            })
            navigateTo({
                url: "/apps/canteenMachine/orderCanteen/index",
                query: {
                    studentId: state.studentId
                }
            })
        })
    })
}
</script>
<style scoped lang="scss">
.container_box {
    background: #f9faf9;
    padding-top: 24rpx;

    .main {
        color: #999999;
        background-color: #fff;
        padding: 32rpx;
        overflow-y: auto;
        position: relative;

        .icon_box {
            position: absolute;
            top: 136rpx;
            right: 30rpx;
            width: 160rpx;
            height: 130rpx;
        }

        .top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20rpx;
            font-weight: 600;
        }

        .text_ident {
            text-align: right;
            font-weight: 600;
            font-size: 28rpx;
        }

        .msg_box {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-size: 28rpx;
            color: #333;
            padding: 24rpx 0;
            border-bottom: 2rpx solid #d5d5d5;
            margin-bottom: 34rpx;

            .r_box {
                overflow: hidden;
                margin-left: 16rpx;
                white-space: nowrap;
                text-overflow: ellipsis;
                font-size: 36rpx;
                font-weight: 600;
            }
        }

        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 28rpx;
            margin-bottom: 40rpx;

            .l_box {
                flex-shrink: 0;
            }

            .r_box {
                word-break: break-all;
            }
        }

        .refund {
            border-top: 1px solid #d9d9d9;
            padding-top: 12px;

            :deep(.is-focused) {
                border-color: var(--primary-color) !important;
            }

            .textarea {
                background: #f9faf9;
            }

            .textarea_box {
                position: relative;

                .count_box {
                    display: inline-block;
                    position: absolute;
                    right: 14rpx;
                    bottom: 6rpx;
                }
            }
        }
    }

    .items_start {
        align-items: flex-start !important;
    }

    .footer_box {
        text-align: right;
        display: flex;
        align-items: center;
        padding: 24rpx 32rpx 60rpx 32rpx;
        justify-content: flex-end;
        background: #fff;

        .btn_box {
            width: 100%;
            height: 70rpx;
            background: #00d190;
            border-radius: 36rpx;
            font-size: 28rpx;
            color: #fff;
            line-height: 68rpx;

            &:after {
                border: none;
            }
        }
    }
}
</style>
