# 项目信息（uniapp 分包应用）
## 1. 项目仓库地址
* [https://git.yyide.com/ydyjopen/h5/yide-applet](https://git.yyide.com/ydyjopen/h5/yide-applet)
* 使用uniapp

## 2. 项目分支
* 正式：main
* 预发布：uatrelease
* 测试：uat
* 本地部署：2.0版本 — local，3.0版本 — local3.0，3.1版本 — local3.1

## 3. 域名信息
* 正式：[https://applet.yyide.com](https://applet.yyide.com)
* 预发布：[https://appletuat.yyide.vip](https://appletuat.yyide.vip)
* 测试：一德：[http://*************:8013](http://*************:8013)（穿透地址： [https://test-158-9528.yyide.vip](https://test-158-9528.yyide.vip)）
* 本地部署环境：[http://************:8013/applet](http://************:8013/applet)（穿透地址： [https://11-h5.yyide.vip/applet](https://11-h5.yyide.vip/applet)）

## 4. env配置
* VITE_BASE_OAH5：OA审批 [项目仓库](https://git.yyide.com/ydyjopen/h5/oa-approve-h5) [项目文档]() 
* VITE_BASE_SHARE：web-app公众号 [项目仓库](https://git.yyide.com/ydyjopen/h5/web-app) [项目文档](../web-app/item-info.md) 
* VITE_BASE_STATIC：静态文件地址
	* 静态文件服务器域名地址：https://file.1d1j.cn/yide-applet
	* | ftp           |     账号      |  密码            |  端口  |
	  | ------------- | :-----------: | :------------:   | :----: |
	  | ************  | upload        | IEplz7ijgWnyGIZS | 22    |
	* 前端部分静态资源放在/usr/local/www/file 目录下 通过https://file.1d1j.cn 访问，ftp 协议为`SFTP` ，当前项目文件目录为`yide-applet`

## 5. 本地部署
* 本地部署构建命令：build:locals，本地部署的代码和正式环境的代码有出入， 每个分支每个版本代码不同，不要乱改

