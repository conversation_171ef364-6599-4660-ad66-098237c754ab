<template>
    <view class="drag">
        <view class="handle">
            <view class="handle-title"> <text class="order_color"> 排序 </text></view>
            <uni-icons class="handle-icon" type="closeempty" size="16" @click="handleClose"></uni-icons>
        </view>
        <view class="content">
            <Draggables :item-height="50" :list="draggableList" @change="dragComplete">
                <template #default="{ item, index }">
                    <view class="type_title">
                        <view class="input">
                            <text class="order_color">
                                {{ orderNumber(index) + "." }}
                            </text>
                            <text class="van-field">{{ item.title }}</text>
                        </view>
                    </view>
                </template>
            </Draggables>
        </view>
        <view class="footer">
            <button class="button" type="primary" style="background-color: var(--primary-color)" @click="dragSave">确定</button>
        </view>
    </view>
</template>

<script setup>
import Draggables from "./index.vue"
const props = defineProps({
    // 列表数据
    draggableList: {
        type: Array,
        default: () => []
    }
})
const emit = defineEmits(["close", "dragSave"])

const dragList = ref([])
// 索引的样式
const orderNumber = (order) => {
    let num = order + 1
    return `${num > 9 ? num : "0" + num}`
}

const handleClose = () => {
    emit("close")
}
// 拖拽完成
function dragComplete(newList, dragItem) {
    console.log(newList, dragItem)
    dragList.value = newList
}

// 确定提交排序
const dragSave = () => {
    emit("dragSave", dragList.value)
}
</script>

<style scoped lang="scss">
@import "../style.scss";
.nowrap {
    padding-left: 4px;
    color: #b8b8b8;
}
.scale {
    margin-top: 20rpx;
    font-size: 26rpx;
}
@mixin justify-space-between {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
}

@mixin padding18($size: 28rpx) {
    padding: $size;
}
.drag {
    width: 95vw;
    height: 80vh;
    

/* #ifdef MP-WEIXIN */
    height: 78vh;

/* #endif */
    position: relative;
    .handle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;
        .handle-title {
            font-size: 40rpx;
            font-weight: 500;
            text-align: center;
            flex: 1;
        }
        .handle-icon {
            width: 40rpx;
        }
    }
    .content {
        padding: 0 20rpx;
        height: calc(100% - 230rpx);
        overflow: hidden auto;
        .type_title {
            @include justify-space-between();
            padding-bottom: 17rpx;

            .three-point {
                @include fontSize(30rpx);
                color: $border-color;
            }
        }
    }
    .footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20rpx;
    }
}
</style>
