$primary-color: var(--primary-color);
$heading-color: #333333;
$white-color: #ffffff;
$gray-background: #f6f6f6;
$gray-background-6ff: #666666ff;
$gray-background-3ff: #333333ff;
$border-color: #d8d8d8;
$background-color-9ff: #f9faf9ff;
$red-color: #ff0000;
$yellow-color: #ffc328ff;
.restrict {
    position: relative;
}
// 清除按钮
.icon_clear {
    position: absolute;
    top: 24rpx;
    right: 8rpx;
    font-size: 30rpx;
    padding: 5rpx;
    z-index: 99;
    color: $gray-background-6ff;
    background-color: $background-color-9ff;
}
.modify_active {
    :deep(.icon) {
        color: var(--primary-color) !important;
    }
}
.vanLoading {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -30rpx;
}
.create_footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 30rpx 30rpx 44rpx;
    background: $white-color;
}
.custom-image {
    padding: 0;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -160rpx;
    margin-top: -200rpx;
}
:deep(.van-haptics-feedback) {
    color: $heading-color;
}
// @include
// 自适应高
@mixin autoHeight($ht: 92rpx) {
    height: calc(100vh - $ht);
    background: $gray-background;
}

// 序号1 颜色
.order_color {
    color: var(--primary-color);
    font-size: 34rpx;
    font-weight: 700;
}

// 字数超出隐藏
@mixin nowrap() {
    flex: 1;
    min-width: 0;
    white-space: nowrap;
}

@mixin nowrap_child($row: 1) {
    -webkit-line-clamp: $row;
    // display:-webkit-box ;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@mixin fontSize($size: 28rpx) {
    font-size: $size;
}

@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {
    .currency {
        padding-bottom: calc(60rpx + constant(safe-area-inset-bottom));
        padding-bottom: calc(60rpx + env(safe-area-inset-bottom));
    }
}
