const types = {
    SelectInput: {
        title: "单选框",
        name: "SelectInput",
        icon: "el-icon-circle-check",
        value: "",
        valueType: "string",
        props: {
            required: false,
            enablePrint: true,
            expanding: false,
            options: ["男", "女"],
            showExpression: {},
            showRule: 0,
        },
    },
    MultipleSelect: {
        title: "多选框",
        name: "MultipleSelect",
        icon: "iconfont icon-duoxuankuang",
        value: [],
        valueType: "array",
        props: {
            required: false,
            enablePrint: true,
            expanding: false,
            options: ["选项1", "选项2"],
            showExpression: {},
            showRule: 0,
        },
    },
    TextareaInput: {
        title: "多行文本输入",
        name: "TextareaInput",
        icon: "el-icon-edit",
        value: "",
        valueType: "string",
        props: {
            required: false,
            enablePrint: true,
            showExpression: {},
            showRule: 0,
        },
    },
    TextInput: {
        title: "单行文本输入",
        name: "TextInput",
        icon: "el-icon-edit",
        value: "",
        valueType: "string",
        props: {
            required: false,
            enablePrint: true,
            showExpression: {},
            showRule: 0,
        },
    },
    Location: {
        title: "地点",
        name: "Location",
        icon: "el-icon-place",
        value: "",
        // {"chian":"广东省,深圳市,宝安区","address":"德弘基创科居","geo":113.883517,"lat":22.576679}
        valueType: "string",
        props: {
            required: false,
            enablePrint: true,
            autoLocate: false,
            options: [""],
            showExpression: {},
            showRule: 0,
        },
    },
    DateTime: {
        title: "日期时间点",
        name: "DateTime",
        icon: "el-icon-date",
        value: "",
        valueType: "date",
        props: {
            required: false,
            enablePrint: true,
            format: "YYYY-MM-DD",
            showExpression: {},
            showRule: 0,
        },
    },
    FileUpload: {
        title: "上传附件",
        name: "FileUpload",
        icon: "el -icon-folder-opened",
        value: [],
        valueType: "array",
        props: {
            required: false,
            enablePrint: true,
            imageOnly: false,
            onlyRead: false, //是否只读，false只能在线预览，true可以下载
            maxSize: 100, //文件最大大小MB
            maxNumber: 10, //最大上传数量
            fileTypes: [], //限制文件上传类型
            showExpression: {},
            showRule: 0,
        },
    },
    SignName: {
        title: "手写签名",
        name: "SignName",
        icon: "el-icon-postcard",
        value: ".jpg",
        valueType: "string",
        props: {
            required: false,
            enablePrint: true,
            showExpression: {},
            showRule: 0,
        },
    },
    DeptPicker: {
        title: "部门选择",
        name: "DeptPicker",
        icon: "iconfont icon-map-site",
        value: [],
        valueType: "dept",
        props: {
            required: false,
            enablePrint: true,
            multiple: false,
            showExpression: {},
            showRule: 0,
        },
    },
    ClassesPicker: {
        title: "班级选择",
        name: "ClassesPicker",
        icon: "el-icon-s-home",
        value: [],
        valueType: "array",
        props: {
            required: false,
            isMultiple: false,
            enablePrint: true,
            showExpression: {},
            showRule: 0,
        },
    },
};
export default types;
