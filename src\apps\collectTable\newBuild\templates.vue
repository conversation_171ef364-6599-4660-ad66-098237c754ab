<!-- templates -->
<template>
    <view class="templats">
        <template v-for="(item, idx) in props.typeList" :key="idx">
            <view class="templats_item" v-if="idx < 2">
                <view class="hander van-hairline--bottom">
                    <span>
                        <span class="order_color">
                            {{ orderNumber(idx) }}
                        </span>
                        <span class="font_size">{{ item.title }}</span>
                    </span>
                    <uni-icons type="more-filled" color="#999" size="13"></uni-icons>
                </view>
                <view class="van-hairline--bottom font_size select" v-if="item.name == 'SelectInput'">
                    <checkbox-group>
                        <view class="checkbox_text" v-for="(item, _idx) in item.props.options" :key="_idx">
                            <checkbox value="cb" checked="true" :color="primaryColor" style="transform: scale(0.7)" />
                            {{ item }}
                        </view>
                    </checkbox-group>
                </view>
                <view class="van-hairline--bottom font_size input" v-else> 请输入 </view>
                <checkbox-group>
                    <view class="checkbox_text">
                        <checkbox :color="primaryColor" :checked="item.props.required"
                            @change="e => item.props.required = e.detail.value" style="transform: scale(0.7)">必填
                        </checkbox>
                    </view>
                </checkbox-group>
            </view>
        </template>
    </view>
</template>

<script setup>
import { computed, reactive } from "vue"

const primaryColor = "var(--primary-color)"
const props = defineProps({
    typeList: {
        type: Array,
        default: () => []
    }
})

const orderNumber = computed(() => {
    return (order) => `${order > 9 ? order : "0" + (order + 1)}.`
})
</script>

<style scoped lang="scss">
@import "../style.scss";

.templats {
    flex: 1;

    .order_color {
        font-size: 24rpx;
    }

    .hander {
        border-bottom: 0.5rpx solid #ebedf0;
    }

    .hander {
        display: flex;
        justify-content: space-between;

        .three-point {
            @include fontSize(18rpx);
            color: $border-color;
        }
    }

    .font_size {
        @include fontSize(22rpx);
    }

    .input {
        color: #999999ff;
    }

    .van-hairline--bottom {
        padding: 8rpx 0;
        text-align: left;
        @include fontSize(22rpx);
    }

    .select {
        :deep(.van-checkbox) {
            margin: 6rpx 0;
        }

        :deep(.van-badge__wrapper) {
            margin-top: 6rpx;
        }

        :deep(.van-badge__wrapper),
        .checkbox_text {
            @include fontSize(24rpx);
        }
    }
}
</style>
