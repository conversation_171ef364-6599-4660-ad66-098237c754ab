<template>
    <div>
        <z-paging ref="paging" class="container_box" v-model="state.dataList" :auto="false" @query="queryList">
            <template #top>
                <view class="top_box">
                    <!-- <uni-search-bar radius="100" v-model="query.itemName" placeholder="请输入缴费项目" cancelButton="none" @confirm="search" /> -->
                    <view class="select_box">
                        <view class="l_box" @click="selectPopupRef.open()">
                            <text class="text">{{ state.orderState.label }}</text>
                            <!-- <view class="r_icon">
                            <uni-icons fontFamily="antiBullying" :size="18">{{ "&#xe62e;" }}</uni-icons>
                        </view> -->
                        </view>
                        <view class="r_box">
                            <text @click="calendars.open()">{{ orderTime }}</text>
                            <view class="r_icon">
                                <uni-icons v-if="orderTime !== '请选择时间范围'" type="clear" size="18" color="#c0c4cd" @click="clearTime" />
                                <!-- <uni-icons v-else fontFamily="antiBullying" :size="18" @click="calendars.open()">{{ "&#xe62e;" }}</uni-icons> -->
                            </view>
                        </view>
                    </view>
                </view>
            </template>
            <view class="container_box">
                <MsgBox :dataList="state.dataList" @handleClick="handleClick" @handlePayment="handlePayment" @initData="initData" @cancelOrder="cancelOrder"></MsgBox>
            </view>
            <template #empty>
                <view class="not_set">
                    <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/b38b5f559f77415aa3bedebe883e09dd.png" alt="" />
                    <text class="text">暂无数据</text>
                </view>
            </template>
        </z-paging>
        <view class="calendar_box">
            <uv-calendars ref="calendars" color="#00D190" :clearDate="false" confirmColor="#00D199" :allowSameDay="true" mode="range" @confirm="confirm" />
        </view>
        <yd-select-popup ref="selectPopupRef" :list="state.list" title="全部订单" @closePopup="closePopup" />
    </div>
</template>
<script setup>
import { onLoad } from "@dcloudio/uni-app"
import MsgBox from "./components/msgBox.vue"
import useStore from "@/store"

const paging = ref(null)
const selectPopupRef = ref(null)
const calendars = ref(null)
const { user } = useStore()

const identityUserId = computed(() => {
    return user?.userInfo?.identityUserId
})

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

const state = reactive({
    studentId: null,
    option: [],
    datetimeRange: [],
    info: {},
    OrderList: [],
    isPay: true,
    dataList: [],
    orderState: {
        value: null,
        label: "全部订单"
    },
    list: [
        { value: null, label: "全部订单" },
        { value: 0, label: "待支付" },
        { value: 1, label: "已支付" },
        { value: 3, label: "已关闭" },
        { value: 2, label: "退款" }
    ]
})

const orderTime = computed(() => {
    if (state.datetimeRange.length == 0) return "请选择时间范围"
    return state.datetimeRange[0].replaceAll("-", ".") + " ~ " + state.datetimeRange[1].replaceAll("-", ".")
})

const query = reactive({
    itemName: "",
    paymentTypeId: null,
    orderTime: "",
    endPayTime: ""
})

const queryList = (pageNo, pageSize) => {
    const [crateTime, endTime] = state.datetimeRange
    const params = {
        pageNo,
        pageSize,
        crateTime,
        endTime,
        orderUserId: identityType.value == "eltern" ? state.studentId : identityUserId.value,
        businessType: 3,
        queryStatus: state.orderState.value
    }
    http.post("/campuspay/mobile/general-pay-order/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const closePopup = (val) => {
    if (!val) return
    state.orderState = val
    paging.value.reload()
}

const confirm = (e) => {
    state.datetimeRange = [e.range.before, e.range.after]
    paging.value.reload()
}

// 清空时间选择
const clearTime = () => {
    state.datetimeRange = []
    paging.value.reload()
}

// 点击列表
const handleClick = (item) => {
    navigateTo({
        url: "/apps/canteenMachine/orderDetail/index",
        query: { id: item.id, studentId: state.studentId }
    })
}
// 支付操作
const handlePayment = (item) => {
    const params = {
        recipientDataIds: [item.recipientDataId],
        tradeNo: item.tradeNo,
        merchantId: item.merchantId,
        payAmountSum: item.payAmount,
        infos: item.itemName,
        payEndTime: item.payEndTime,
        orderNo: item.sceneNo,
        title: item.title,
        payType: item.payMethod
    }
    navigateTo({
        url: "/apps/canteenMachine/orderPayment/index",
        query: {
            ...params,
            recipientDataIds: item.recipientDataId,
            studentId: state.studentId
        }
    })
}

// 取消订单
const cancelOrder = (item) => {
    const params = {
        tradeNo: item.tradeNo,
        orderUserId: identityType.value == "eltern" ? state.studentId : identityUserId.value
    }
    http.post("/campuspay/mobile/general-pay-order/cancel/order", params).then((res) => {
        uni.showToast({
            title: "取消订单成功",
            icon: "success",
            duration: 2000
        })
        initData()
    })
}

// 重新获取数据
const initData = () => {
    query.itemName = ""
    query.paymentTypeId = null
    state.datetimeRange = []
    paging.value.reload()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    nextTick(async () => {
        state.studentId = options.studentId || null
        paging.value.reload()
    })
})
</script>

<style lang="scss">
.uni-picker-header {
    .uni-picker-action-confirm {
        color: var(--primary-color) !important;
    }
}

.calendar_box {
    .uv-toolbar__wrapper__confirm {
        color: var(--primary-color) !important;
    }

    .uv-calendar-item__weeks-box-item {
        .uv-calendar-item--isDay-text {
            color: var(--primary-color) !important;
        }
    }
}
</style>
<style scoped lang="scss">
.container_box {
    .calendar_box {
        .uv-calendar-item__weeks-box-item {
            .uv-calendar-item--isDay-text {
                color: var(--primary-color) !important;
            }
        }
    }

    :deep(.uni-list-item__container) {
        padding: 0;
        background: transparent;
    }

    .top_box {
        padding: 32rpx 30rpx;
        background: #fff;

        :deep(.uni-searchbar) {
            padding: 0;
        }

        .uni-searchbar {
            padding: 0;
        }

        .select_box {
            margin-top: 24rpx;
            font-size: 28rpx;
            display: flex;
            justify-content: space-between;

            .l_box {
                border-radius: 8rpx;
                display: flex;
                align-items: center;
                .text {
                    display: flex;
                    align-items: center;

                    &::after {
                        content: "";
                        display: block;
                        border: 10rpx solid transparent;
                        border-top: 10rpx solid var(--primary-color);
                        border-bottom-width: 1px;
                        margin-left: 10rpx;
                    }
                }
            }

            .r_box {
                box-sizing: border-box;
                display: flex;
                justify-content: space-between;
                padding: 0 0 0 16rpx;
                display: flex;
                align-items: center;
                border-radius: 8rpx;

                :deep(.uni-date) {
                    width: calc(100% - 50rpx);
                }

                .text {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: #333;
                    display: flex;
                    align-items: center;
                }
            }

            .r_icon {
                width: 50rpx;
                margin-left: 10rpx;
                flex-shrink: 0;
                display: flex;
                align-items: center;

                &::after {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    border-top: 10rpx solid var(--primary-color);
                    border-bottom-width: 1px;
                    margin-left: 10rpx;
                }
            }
        }
    }
}

.not_set {
    height: calc(100vh - 140rpx);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .image {
        width: 360rpx;
        height: 210rpx;
    }

    .text {
        font-size: 26rpx;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 36rpx;
        padding-top: 30rpx;
    }
}
</style>
