# H5项目信息整理
## 项目分类
+ 使用vant组件： **appH5** ，**web-app**，使用uniapp: **yide-applet**


::: info 一加壹使用到的应用
+ **yide-applet**：巡逻巡查，邀请加入， 学生考勤，班级德育，宿舍，访客系统，成绩管理，活动报名

+ **appH5**：周报，通知公告编辑功能，场地预约，收集表，投票，访客系统，通行，考勤
:::

::: info web-app公众号使用到的应用
+ **appH5**：收集表，场地预约，投票活动

+ **yide-applet**：巡逻巡查，访客，班级德育，成绩管理，活动报名，打卡，社团管理，教师考勤
:::

## 可以复用的方法
+ 1. 获取当前环境方法 checkPlatform
+ 2. 登录时数据RSA加密（在web-app中）
+ 3. uniapp获取静态文件 getStaticFile 
+ 4. uniapp路由跳转方法 navigateTo
+ 5. uniapp复制链接 copyUrl
+ 6. uniapp设置获取缓存方法 Storage
+ 7. VConsole的使用


## 已写过的应用总和
+  1.周报  2.通知公告编辑功能 3. 场地预约 4. 倒计时（暂时无用） 5. 学生留言（意见箱） 6. 收集表 7. 投票 8. 访客系统 9. 校园考勤 10. 教师考勤 11. 课表 12. 信息发布 13. 通行 14. 今日作业 15. 校园视频 16. 校园风采 17. 巡逻/巡查/巡检 18. 邀请加入页面 19. 个人信息录入 20. 学生考勤 21. 我的学生  22. 班级德育 23. 智慧宿舍 24. 成绩管理 25. 活动报名 26. 教师考勤 27. 社团管理 28. 打卡（我的任务） 29. 请假 30. 投票活动 31. 校易付 32. 缴费系统 33. 群组消息 34. 审批 35. 待办 36. 食堂 37. 考试管理 38. 预警消息


## 缺少的应用
+ 1.值日生 2. 倒计时 3. 日程 4. 校历 5. 智慧班牌 6. 电子学生证 7. 评价系统 8. 荣誉墙 9. 意见箱 10. 会议 11. 班级圈 12. 财务报表 14. RFID系统 15. 智慧图书馆 16. 课堂表现 17. 学生证 18. 家长请假，学生请假，教师请假（已有一个不一样的） 19. 出差 20. 学生终端设备申请 21. 我的班级

