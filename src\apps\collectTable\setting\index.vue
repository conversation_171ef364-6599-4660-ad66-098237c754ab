<template>
    <view class="set">
        <view class="set-contont">
            <uni-nav-bar v-show="!isSelectorRef" class="nav" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="收集设置" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>

            <view @click="openSelector">
                <uni-list-item showArrow title="收集对象与范围" link :rightText="collectTable.updateSaveForm.businessParam.collectObj" />
            </view>

            <uni-list-item title="是否允许外部人员填写" class="allowAnonymousText">
                <template v-slot:footer>
                    <switch @change="handelrdd" :checked="collectTable.updateSaveForm.businessParam.allowAnonymous" color="#00b781" style="transform: scale(0.7)" />
                </template>
            </uni-list-item>
            <uni-list-item class="reset-text" title="允许外部人员填写则可以将收集表发送在其他移动终端上进行填写"> </uni-list-item>
            <view class="clear"> </view>

            <uni-list-item showArrow title="收集方式" :rightText="collectTypes" link @click="popup.open('bottom')" />

            <uni-list-item title="非工作日不收集">
                <template v-slot:footer>
                    <switch :checked="collectTable.updateSaveForm.businessParam.collectInWeekend" color="#00b781" style="transform: scale(0.7)" />
                </template>
            </uni-list-item>

            <uni-list-item title="每日收集提醒">
                <template v-slot:footer>
                    <switch :checked="collectTable.updateSaveForm.businessParam.collectRemind" color="#00b781" style="transform: scale(0.7)" />
                </template>
            </uni-list-item>

            <uni-list-item showArrow title="提醒时间" :rightText="collectTable.updateSaveForm.businessParam.remindTime" link @click="datetimePicker.open()" />

            <uv-datetime-picker ref="datetimePicker" v-model="collectTable.updateSaveForm.businessParam.remindTime" mode="time" confirmColor="#11C685" @confirm="confirm"> </uv-datetime-picker>

            <uni-popup ref="popup" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
                <view class="popup-content">
                    <view class="handle">
                        <view class="handle-title"> <text class="order_color"> 收集方式 </text></view>
                        <uni-icons class="handle-icon" type="closeempty" size="16" @click="popup.close()"></uni-icons>
                    </view>
                    <view class="content">
                        <radio-group @change="radioChange">
                            <view class="content-item" v-for="it in statusTypes" :key="it.value">
                                <label class="uni-list-cell">
                                    <view>{{ it.text }}</view>
                                    <view>
                                        <radio :value="JSON.stringify(it)" color="#00b781" :checked="it.value == checked" />
                                    </view>
                                </label>
                            </view>
                        </radio-group>
                    </view>
                </view>
            </uni-popup>
        </view>
        <view class="set-footer">
            <button class="save-btn" type="primary" :loading="collectTable.loading" @click="handerAccomplish">确定</button>
        </view>

        <YdSelectors ref="selectorRef" @confirm="confirmFn" v-model:isSelectorRef="isSelectorRef" />
    </view>
</template>

<script setup>
import { reactive, inject } from "vue"
import YdSelectors from "../YdSelectors.vue"
import useStore from "@/store"
const { collectTable } = useStore()
const selectorRef = ref(null)
// 收集方式
const collectTypes = computed(() => {
    return collectTable.updateSaveForm.businessParam.collectMethod ? "仅收集一次" : "每日收集一次"
})
const checked = computed(() => {
    return collectTable.updateSaveForm.businessParam.collectMethod
})
const isSelectorRef = ref(false)
const datetimePicker = ref()
const popup = ref()

const statusTypes = [
    {
        value: 0,
        text: "每日收集一次",
        checked: true
    },
    {
        value: 1,
        text: "仅收集一次",
        checked: false
    }
]
const state = reactive({
    loadingBtn: false
})
const confirm = (evt) => {
    collectTable.updateSaveForm.businessParam.remindTime = evt.value
}
const radioChange = (evt) => {
    const { text, value } = JSON.parse(evt.detail.value)
    collectTable.updateSaveForm.businessParam.collectMethod = value
    popup.value.close()
}

function openSelector() {
    const typeList = [
        {
            type: "dept",
            name: "部门",
            selectLevel: "dept" // 选填
        },
        {
            type: "parent",
            name: "家长",
            selectLevel: "all" // 必填
        },
        {
            type: "classes",
            name: "班级",
            selectLevel: "all" // 必填
        }
    ]
    selectorRef.value.open(typeList, true)
}
function confirmFn(ids, selected) {
    const userName = []
    collectTable.updateSaveForm.businessParam.scopeList = selected.map((v) => {
        userName.push(v.name)
        let params = {
            scopeIdentity: 0,
            scopeId: v.id,
            scopeType: 0
        }
        if (v.typeValue == "dept") {
            params.scopeIdentity = 1
            params.scopeType = 0 // 部门
        } else if (v.typeValue == "parent") {
            params.scopeIdentity = 2
            params.scopeType = 1 // 部门
        } else {
            params.scopeIdentity = 0
            params.scopeType = 1 // 学籍
        }
        return params
    })
    collectTable.updateSaveForm.businessParam.collectObj = userName.length ? userName.join(",") : ""
}

const handerAccomplish = () => {
    if (!collectTable.updateSaveForm.businessParam.collectObj) {
        uni.showToast({ title: "收集对象与范围不能为空！", icon: "none" })
        return
    }
    const params = collectTable.updateSaveForm
    state.loadingBtn = true
    http.post("/cloud/formDesigner/create", params)
        .then((res) => {
            uni.showToast({ title: res.message })
            navigateTo({
                url: "/apps/collectTable/accomplish"
            })
            collectTable.resetDetComponents()
        })
        .finally(() => {
            state.loadingBtn = false
        })
}
// 返回
function clickLeft() {
    uni.navigateBack(1)
}
</script>
<style scoped lang="scss">
.set {
    background-color: #f6f6f6;
    height: 100vh;

    .set-contont {
        .allowAnonymousText {
            :deep(.uni-list-item__content) {
                justify-content: center;
            }
        }

        .reset-text {
            padding-top: 14rpx;
            line-height: 40rpx;

            :deep(.uni-list-item__container) {
                padding: 0 22rpx 12rpx;
                font-size: 12px;
                color: #999;
            }
        }

        .clear {
            height: 20rpx;
        }

        .nav {
            margin-bottom: 20rpx;
        }

        .popup-content {
            .handle {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20rpx;

                .handle-title {
                    font-size: 40rpx;
                    font-weight: 500;
                    text-align: center;
                    flex: 1;
                }

                .handle-icon {
                    width: 40rpx;
                }
            }

            .content {
                .uni-list-cell {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20rpx;
                    border-bottom: 1rpx solid #eee;
                }
            }
        }
    }

    .set-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        padding: 20rpx;

        .save-btn {
            background-color: var(--primary-color);
        }
    }
}
</style>
