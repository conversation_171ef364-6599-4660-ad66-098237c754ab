<template>
    <view class="stat_container_box">
        <view class="box">
            <view v-for="(item, index) in list" :key="index">
                <view :class="['item', { danger: item.flag === 4 }]" :style="{ width: width }" @click="handleClick(item)">
                    <view class="top">{{ item.value }}</view>
                    <view>{{ item.name }}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
defineProps({
    list: {
        type: Array,
        default: () => []
    },
    width: {
        type: String,
        default: "170rpx"
    }
})

const emit = defineEmits(["handleClick"])
const handleClick = (val) => {
    emit("handleClick", val)
}
</script>

<style lang="scss">
.stat_container_box {
    .box {
        background-color: $uni-bg-color-grey;
        border-radius: 20rpx;
        overflow: hidden;
        display: flex;
        padding: 26rpx 0rpx;
        .item {
            text-align: center;
            font-size: 28rpx;
            position: relative;
            border-right: 1rpx solid #d9d9d9;
            &:last-of-type {
                border: none;
            }
            .top {
                font-size: 40rpx;
                margin: 10rpx 0 16rpx 0;
            }
        }
        .danger {
            color: #f5222d;
        }
    }
}
</style>
