<template>
    <div>
        <z-paging ref="paging" class="page">
            <template #top>
                <uni-nav-bar v-if="hideNavBar" left-icon="left" title="智慧点餐" :border="false" fixed statusBar @clickLeft="routerBack" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
                <view class="header">
                    <view class="title">
                        <text class="main_title">智慧点餐</text>
                        <view class="user_btn" @click="handleSelect" v-if="identityType == 'eltern'">
                            <text>{{ state.studentInfo.studentName }}</text>
                            <image class="change_over" src="@nginx/workbench/canteenMachine/changeOver.png" mode="scaleToFill" />
                        </view>
                    </view>
                    <view class="week_selector">
                        <view class="arrow_btn">
                            <uni-icons type="left" size="20" color="#333" @click="handleWeek('prev')"></uni-icons>
                        </view>
                        <text class="current_week">{{ week }}</text>
                        <view class="arrow_btn">
                            <uni-icons type="right" size="20" color="#333" @click="handleWeek('next')"></uni-icons>
                        </view>
                        <view class="header_btns">
                            <view class="stats_btn record" @click="orderingRecord">点餐记录</view>
                            <view class="stats_btn" @click="handleToStat">费用统计</view>
                        </view>
                    </view>
                    <view class="calendar">
                        <view v-for="(item, index) in state.days" :key="index" class="day_item" @click="handleDay(index, item)">
                            <text class="weekday">{{ item.week }}</text>
                            <text :class="['date', state.active == index ? 'active' : '']">{{ item.day }}</text>
                            <view :class="['dot', item.isOrderedFood == 1 || item.isOrdered ? 'has_order' : 'no_order']"> </view>
                        </view>
                    </view>
                </view>
            </template>
            <view class="mian_box">
                <template v-for="(i, idx) in state.days" :key="idx">
                    <template v-if="i.date == state.days[state.active].date">
                        <template v-if="i.list.length">
                            <div class="week_calendar" id="weekCalendar">
                                <div class="left_classify">
                                    <div
                                        class="classify"
                                        :class="{
                                            active_classify: classify.mealSetTypeId == selectMealSetTypeId
                                        }"
                                        @click="handleClassify(classify)"
                                        v-for="(classify, classifyIdx) in i.list"
                                        :key="classify.name + classifyIdx"
                                    >
                                        <view class="active_line" v-if="classify.mealSetTypeId == selectMealSetTypeId"> </view>
                                        <text>{{ classify.name }}</text>
                                        <view
                                            class="classify_status"
                                            :style="{
                                                background: classifyStatus(
                                                    classify,
                                                    {
                                                        0: '#FFB860',
                                                        1: 'var(--primary-color)'
                                                    },
                                                    '#FF6060'
                                                )
                                            }"
                                        >
                                            <text>{{ classifyStatus(classify, { 0: "未点", 1: "已点" }, "不预定") }}</text>
                                        </view>
                                    </div>
                                </div>
                                <scroll-view class="right_commodity" :scroll-y="true" :scroll-top="state.scrollTop" @scroll="scroll">
                                    <view v-for="(item, index) in i.list" :key="item.mealSetTypeId">
                                        <view v-show="item.mealSetTypeId == selectMealSetTypeId">
                                            <!-- 提示 -->
                                            <view class="tip_box" v-if="index == 0 && state.days[state.active].deadline">
                                                <uni-icons type="info-filled" size="16" color="#FFA052" style="margin-right: 10rpx"></uni-icons>
                                                预定套餐截止时间：{{ state.days[state.active].deadline }}
                                            </view>
                                            <!-- 套餐 -->
                                            <view class="type_title">
                                                {{ item.name }}
                                            </view>
                                            <view>
                                                <view class="meal_section" v-for="(child, j) in item.children" :key="`${j}_${child.name}`">
                                                    <template v-if="child.mealSetDishes.length || child.singlePointDishes.lengt">
                                                        <view class="meal_header">
                                                            <view class="meal_title">{{ child.name }}</view>
                                                            <view v-if="isBefore && !isOrdere(item)">
                                                                <template v-if="child.isCheck">
                                                                    <uni-icons type="checkbox-filled" size="28" color="#cbcbcb" v-if="item.isTodayOrder == 0"></uni-icons>
                                                                    <uni-icons type="checkbox-filled" size="28" color="var(--primary-color)" v-else @click="handleCheck(child, item.children)"></uni-icons>
                                                                </template>
                                                                <template v-else>
                                                                    <view :class="['circle', item.isTodayOrder == 0 ? 'poiner_none' : '']" @click="handleCheck(child, item.children)"></view>
                                                                </template>
                                                            </view>
                                                        </view>
                                                        <view class="dish_list">
                                                            <view class="dish_item" v-for="base in child.mealSetDishes" :key="base.dishId">
                                                                <image class="dish_image" :src="base.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                                                                <view class="dish_content" style="justify-content: center">
                                                                    <text class="dish_name">{{ base.dishName }}</text>
                                                                </view>
                                                            </view>
                                                        </view>
                                                        <view class="extra_section" v-if="child.singlePointDishes.length > 0">
                                                            <text class="extra_title">自选额外菜品:</text>
                                                            <view class="dish_list">
                                                                <view class="dish_item" v-for="extra in child.singlePointDishes" :key="extra.dishId">
                                                                    <image class="dish_image" :src="extra.dishLogo || 'https://alicdn.1d1j.cn/1634048696205205505/default/d21efdf1d2924840bfed4b9f6a3a9067.png'" mode="aspectFill"></image>
                                                                    <view class="dish_content">
                                                                        <text class="dish_name">{{ extra.dishName }}</text>
                                                                        <view class="dish_price"
                                                                            >¥{{ extra.dishPrice }}
                                                                            <view v-if="isBefore && !isOrdere(item)">
                                                                                <template v-if="extra.isCheck">
                                                                                    <uni-icons type="checkbox-filled" size="28" color="#cbcbcb" v-if="item.isTodayOrder == 0"></uni-icons>
                                                                                    <uni-icons type="checkbox-filled" size="28" color="var(--primary-color)" v-else @click="handleCheck(extra)"></uni-icons>
                                                                                </template>
                                                                                <template v-else>
                                                                                    <view :class="['circle', item.isTodayOrder == 0 ? 'poiner_none' : '']" @click="handleCheck(extra)"></view>
                                                                                </template>
                                                                            </view>
                                                                        </view>
                                                                    </view>
                                                                </view>
                                                            </view>
                                                        </view>
                                                        <view class="total_price">
                                                            <text
                                                                :style="{
                                                                    color: child.isEat == 0 ? '#F5222D' : 'var(--primary-color)'
                                                                }"
                                                            >
                                                                <template v-if="child.isEat !== null">
                                                                    <text v-if="child.isEat == 1">{{ child.eatTime }}</text>
                                                                    <text>{{ child.isEat == 0 ? "未就餐" : "已就餐" }}</text>
                                                                </template>
                                                            </text>
                                                            <text>合计：{{ totalPrice(child) }}元</text>
                                                        </view>
                                                    </template>
                                                </view>
                                            </view>
                                            <!-- 本餐不预定-->
                                            <view class="meal_section" v-if="isBefore">
                                                <view v-if="!isOrdere(item) && !isTodayOrder(item)" class="meal_header" @click="handleReserve(item, item.children)">
                                                    <text class="meal_title">本餐不预定</text>
                                                    <uni-icons v-if="item.isTodayOrder == 0" type="checkbox-filled" size="28" color="var(--primary-color)"></uni-icons>
                                                    <view class="circle" v-else></view>
                                                </view>
                                                <div v-if="isTodayOrder(item)" class="unscheduled">
                                                    <image class="unscheduled_image" src="@nginx/workbench/canteenMachine/unscheduled.png" mode="scaleToFill" />
                                                    已选择本餐不预定，如需预定请在截止时间前取消订餐，再重新点餐即可
                                                </div>
                                            </view>
                                            <button class="nav_btn btn_prev" v-if="classifyStatus(item, { 0: '未点', 1: '已点' }, '不预定') != '未点'" @click="cancelOrder(item)">取消订餐</button>
                                            <view class="nextbox">
                                                <button class="nav_btn btn_next" :loading="confirmLoading" @click="dialogConfirm(i.date, item)" v-if="classifyStatus(item, { 0: '未点', 1: '已点' }, '不预定') == '未点'">
                                                    <span class="selected_text">已选：{{ selectNum }} </span>
                                                    <div class="right_confirm">确认订餐</div>
                                                </button>
                                            </view>
                                        </view>
                                    </view>
                                    <view
                                        :style="{
                                            height: scrollBottom(i)
                                        }"
                                    >
                                    </view>
                                </scroll-view>
                            </div>
                        </template>
                        <template v-else>
                            <div class="not_set">
                                <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/b38b5f559f77415aa3bedebe883e09dd.png" alt="" />
                                <span class="text">暂无数据</span>
                            </div>
                        </template>
                    </template>
                </template>
            </view>
        </z-paging>
        <yd-select-popup ref="selectPopupRef" :list="state.studentList" title="请选择" @closePopup="closePopup" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[state.studentInfo.studentId]" />

        <yd-popup ref="confirmRef" :titleflag="false" confirmText="确认预定" cancelColor="var(--primary-color)" @confirm="dialogConfirm">
            <view style="color: #666; font-size: 28rpx; padding-top: 40rpx">请确定选择的菜品是否符合口味</view>
        </yd-popup>
    </div>
</template>
<script setup>
import { checkPlatform } from "@/utils/sendAppEvent.js"
import dayjs from "dayjs"
import showModal from "./showModal.js"
import useStore from "@/store"
import { computed, nextTick } from "vue"

const { user, system } = useStore()
// 叮叮环境如果是单独免登则隐藏导航栏
const hideNavBar = computed(() => {
    return checkPlatform() !== "dingding"
})
const selectMealSetTypeId = ref(null)
const oldScrollTop = ref(0)
const confirmLoading = ref(false)
const confirmRef = ref(null)
const selectPopupRef = ref(null)
const userType = ref(null)

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

// 是否已经点餐
const isOrdere = computed(() => {
    return (item) => {
        return item.children.some((i) => i.isOrderedFood == 1)
    }
})

const isTodayOrder = computed(() => {
    return (item) => {
        return item.children.some((i) => i.isTodayOrder == 0)
    }
})

const classifyStatus = computed(() => {
    return (val, colorObj, todayColor) => {
        // 是否已点
        const orderedStatus = isOrdere.value(val) ? 1 : 0
        // 是否预定
        const todayOrderStatus = val.children.some((i) => i.isTodayOrder === 0) ? 0 : 1
        if (orderedStatus == 0 && todayOrderStatus == 0) {
            return todayColor
        } else {
            return colorObj[orderedStatus]
        }
    }
})

const scrollBottom = computed(() => {
    return (i) => {
        try {
            const obj = i.list.find((item) => item.mealSetTypeId == selectMealSetTypeId.value)
            const status = classifyStatus.value(obj, { 0: "未点", 1: "已点" }, "不预定")
            return status && status == "未点" ? "70px" : 0
        } catch (error) {
            return 0
        }
    }
})

const state = reactive({
    days: [],
    active: 0,
    checkNum: 1,
    studentInfo: {},
    studentList: [],
    scrollTop: 0
})

// 最后一条未订餐下标
const lastIndex = computed(() => {
    let lastIndex = -1
    state.days.forEach((i, index) => {
        if (i.isOrderedFood == 0) {
            lastIndex = index
        }
    })
    return lastIndex
})
// 垂直选项卡高度
const height = computed(() => uni.getSystemInfoSync().windowHeight - 195 - 30 - 40 - 44)
// 当前日期是否在选中日期之前(不包含今天)
const isBefore = computed(() => dayjs().subtract(1, "day").isBefore(dayjs(state.days[state.active]?.date)))

const selectNum = ref(0)

// 周区段显示
const week = computed(() => {
    const day = dayjs().format("YYYY-MM-DD")
    const isWeek = state.days.some((i) => i.date == day)
    if (isWeek) return "本周"
    const startDay = state.days[0]?.date.slice(-5).replace("-", ".") || ""
    const endDay = state.days[state.days.length - 1]?.date.slice(-5).replace("-", ".") || ""
    return `${startDay} - ${endDay}`
})

// 日期切换
const handleDay = (index, item) => {
    // console.log(item);
    state.active = index
    selectNum.value = 0
    const defSelect = state.days[index].list.find((i) => {
        return i.isTodayOrder != 0 && !isOrdere.value(i)
    })
    selectMealSetTypeId.value = defSelect?.mealSetTypeId || state.days[index].list[0]?.mealSetTypeId || null
}

// 周切换
const handleWeek = (val) => {
    if (val === "prev") {
        state.checkNum--
    } else {
        state.checkNum++
    }
    const params = getWeekRange(state.checkNum, false)
    getweekList({ ...params })
}

// 打开弹窗
const handleSelect = () => {
    selectPopupRef.value.open()
}

// 学生弹窗关闭
const closePopup = (val) => {
    if (!val) return
    state.studentInfo = val
    getweekList()
    state.checkNum = 1
}
// 上一步
const handlePrev = () => {
    if (state.active == 0) return
    state.active--
}

// 下一步
const handleNext = async (val) => {
    await dialogConfirm()
    const index = state.days.findIndex((item, index) => {
        if (val < index && item.isOrderedFood == 0) {
            return true
        }
    })
    index > -1 ? (state.active = index) : state.active++
}

// 点餐统计
const handleToStat = () => {
    navigateTo({
        url: "/apps/canteenMachine/orderStat/index",
        query: {
            studentId: state.studentInfo.studentId || ""
        }
    })
}

const orderingRecord = () => {
    navigateTo({
        url: "/apps/canteenMachine/orderRecord/index",
        query: {
            studentId: state.studentInfo.studentId || ""
        }
    })
}

const cancelOrder = (item) => {
    item.isTodayOrder = 0
    const params = {
        userId: state.studentInfo.studentId || "",
        userType: userType.value,
        ids: item.children.map((i) => i.id)
    }
    showModal({
        title: "提示",
        content: "是否确认取消本次订餐？",
        success: async () => {
            await http.post("/app/canteen/canteenStudentOrder/delete", params)
            uni.showToast({
                title: "取消订单成功",
                icon: "none"
            })
            const obj = getWeekRange(state.checkNum, false)
            await getweekList({ ...obj })
        }
    })
}

const handleSubmit = () => {
    confirmRef.value.open()
}

const submitOrder = async (arr) => {
    const { message } = await http.post("/app/canteen/canteenStudentOrder/create", {
        canteenDishInfoList: arr,
        userId: state.studentInfo.studentId || "",
        userType: userType.value,
        classesName: state.studentInfo.studentName
    })
    return message
}

const dialogConfirm = async (date, classify) => {
    if (!confirmLoading.value) {
        confirmLoading.value = true
        try {
            const arr = []
            if (classify.isTodayOrder == 0) {
                arr.push({
                    mealSetTypeId: classify.mealSetTypeId,
                    isTodayOrder: 0,
                    orderDate: date
                })
            } else if (classify.children.length) {
                classify.children.forEach((i) => {
                    if (i.isCheck) {
                        const singlePointDishList = i.singlePointDishes.filter((i) => i.isCheck).map((i) => i.dishId)
                        const obj = {
                            isTodayOrder: classify.isTodayOrder,
                            orderDate: date,
                            mealSetTypeId: classify.mealSetTypeId,
                            mealSetId: i.mealSetId,
                            singlePointDishList
                        }
                        arr.push(obj)
                    }
                })
            }
            const message = await submitOrder(arr)
            const params = getWeekRange(state.checkNum, false)
            await getweekList({ ...params })
            const defSelect = state.days[state.active].list.find((i) => {
                return i.isTodayOrder != 0 && !isOrdere.value(i)
            })
            const selectOrder = state.days[state.active].list.filter((i) => {
                return isOrdere.value(i) || i.isTodayOrder == 0
            })
            selectMealSetTypeId.value = defSelect?.mealSetTypeId || null
            if (selectOrder && selectOrder.length) {
                if (selectOrder.length == state.days[state.active].list.length) {
                    if (state.active == 6) {
                        state.checkNum++
                        state.active = 0
                    } else {
                        state.active++
                    }
                    selectMealSetTypeId.value = defSelect?.mealSetTypeId || null
                    const params = getWeekRange(state.checkNum, false)
                    await getweekList({ ...params })
                }
            }
            selectNum.value = 0
            confirmLoading.value = false
            message &&
                uni.showToast({
                    title: message,
                    icon: "none"
                })
        } catch (error) {
            confirmLoading.value = false
        }
    }
}

// 当日是否预定切换
const handleReserve = (item, child) => {
    if (!isOrdere.value(item) && !isTodayOrder.value(item)) {
        item.isTodayOrder = !item.isTodayOrder ? 1 : 0
    }
}

// 选中切换
const handleCheck = (item, data) => {
    if (item.isCheck) {
        item.isCheck = !item.isCheck
        item.singlePointDishes?.forEach((i) => (i.isCheck = false))
        if (item.isSingleDish) {
            item.isCheck ? selectNum.value++ : selectNum.value--
        } else {
            selectNum.value = data?.filter((item) => item.isCheck).length || 0
        }
        return
    } else if (data) {
        data.forEach((i) => {
            i.isCheck = false
            i.singlePointDishes?.forEach((i) => (i.isCheck = false))
        })
    }
    item.isCheck = !item.isCheck
    if (item.isSingleDish) {
        item.isCheck ? selectNum.value++ : selectNum.value--
    } else {
        selectNum.value = data?.filter((item) => item.isCheck).length || 0
    }
    item.isOrderedFood = item.isOrderedFood = 0 ? 1 : 0
    data?.forEach((i) => {
        i.isOrderedFood = i.isOrderedFood = 0 ? 1 : 0
    })
}

// 套餐金额合计
const totalPrice = computed(() => {
    return (data) => {
        let total = data.price
        let singlePointDishes = []
        if (state.days[state.active].isCheck) {
            singlePointDishes = []
        } else {
            singlePointDishes = data.singlePointDishes.filter((i) => i.isCheck)
        }
        const single = singlePointDishes.reduce((accumulator, currentItem) => {
            return accumulator + parseFloat(currentItem.dishPrice)
        }, 0)
        return (total + single).toFixed(2)
    }
})

watch(
    () => state.days[state.active],
    (val) => {
        if (val && val.list) {
            // 当日是否有订餐
            const orderedFood = val.list.find((i) => i.badge.value !== "")
            if (orderedFood) {
                state.days[state.active].isOrdered = true
            } else {
                state.days[state.active].isOrdered = false
            }
            if (!selectMealSetTypeId.value) {
                const defSelect = state.days[state.active].list.find((i) => {
                    return i.isTodayOrder != 0 && !isOrdere.value(i)
                })
                selectMealSetTypeId.value = defSelect?.mealSetTypeId || state.days[state.active].list[0]?.mealSetTypeId || null
            }
        }
    },
    {
        deep: true,
        immediate: true
    }
)

// 获取周订餐列表
const getweekList = async (params = {}) => {
    try {
        uni.showLoading({
            title: "加载中",
            mask: true
        })
        state.days = []
        const { data } = await http.post("/app/canteen/canteenStudentOrder/canteenStudentWeekOrderList", {
            ...params,
            userId: state.studentInfo.studentId || null,
            userType: userType.value
        })

        state.days = disposalData(data)
        console.log("__data__", state.days)
        uni.hideLoading()
    } catch (e) {
        uni.hideLoading()
    }
}

function scroll(e) {
    oldScrollTop.value = e.detail.scrollTop
}

function handleClassify(classify) {
    state.scrollTop = oldScrollTop.value
    nextTick(() => {
        state.scrollTop = 0
        selectMealSetTypeId.value = classify.mealSetTypeId
        selectNum.value = 0
    })
}

function disposalData(sourceData) {
    if (!sourceData) return
    const weeks = sourceData.weeks
    const mealSetTypes = sourceData.mealSetTypes

    // 初始化 obj
    const obj = {}
    weeks?.forEach((i) => {
        obj[i.date] = {
            week: i.week,
            date: i.date,
            isOrdered: false,
            isOrderedFood: i.isOrderedFood,
            isTodayOrder: i.isTodayOrder,
            day: i.day,
            deadline: i.deadline,
            list: []
        }
    })

    // 处理 mealSetTypes
    mealSetTypes?.forEach((item) => {
        const typeData = new Map()

        if (item.mealSets && item.mealSets.length > 0) {
            item.mealSets.forEach((i) => {
                if (!typeData.has(i.orderDate)) {
                    typeData.set(i.orderDate, {
                        name: item.mealSetTypeName,
                        mealSetTypeId: item.mealSetTypeId,
                        mealSetTypeCode: item.mealSetTypeCode,
                        badge: { value: "" },
                        children: []
                    })
                }
                typeData.get(i.orderDate).children.push(i)
            })
        }

        // 合并数据到 obj
        for (const [key, value] of typeData.entries()) {
            if (obj[key]) {
                obj[key].list.push(value)
            }
        }
    })

    const arrData = []
    for (const key in obj) {
        arrData.push(obj[key])
    }
    console.log(arrData, "obj")
    arrData.forEach((i) => {
        i.list.forEach((child) => {
            child.isTodayOrder = child.children.some((i) => i.isTodayOrder === 0) ? 0 : 1
        })
    })
    return arrData
}

// 获取指定周数后的日期范围
function getWeekRange(weeksAfter = 0, returnFullWeek = true) {
    // 如果没有提供 weeksAfter 参数，则使用当前日期作为基准
    let baseDate = weeksAfter === undefined ? dayjs() : dayjs().add(weeksAfter, "week")

    // 找到 baseDate 所在周的周一（一周的开始）
    let startOfWeek = baseDate.subtract(baseDate.day() === 0 ? 6 : baseDate.day() - 1, "day")

    if (returnFullWeek) {
        // 初始化日期数组和当前处理日期
        let dateArray = []
        let currentDate = startOfWeek.clone()

        // 循环添加从本周的开始到结束的所有日期
        for (let i = 0; i < 7; i++) {
            dateArray.push(currentDate.format("YYYY-MM-DD"))
            currentDate = currentDate.add(1, "day")
        }

        return dateArray
    } else {
        // 获取该周的结束日期（周日）
        let endOfWeek = startOfWeek.add(6, "day") // 加上6天得到本周的最后一天

        return {
            startTime: startOfWeek.format("YYYY-MM-DD"),
            endTime: endOfWeek.format("YYYY-MM-DD")
        }
    }
}

function getOpenId(code) {
    http.get("/campuspay/callback/wechat/gzh/getAccessToken", {
        code
    }).then((res) => {
        system.setAppData({ sys: "canteenMachine", data: { openid: res.data.openid } })
    })
}

const getUrlParams = (isShare, _url) => {
    const params = {}
    const url = _url || window.location.search || window.location.hash
    if (url.indexOf("?") > -1) {
        const str = url.split("?")[1]
        const arr = str.split("&")
        arr.forEach((item) => {
            const [key, value] = item.split("=")
            params[key] = value
        })
        return params
    } else {
        return params
    }
}
// 判断不同设备
let ua = navigator.userAgent

// 获取微信code
async function getWeiXinCode() {
    const VITE_APP_NAME = import.meta.env.VITE_APP_NAME
    const urlObj = {
        dev: "http://192.168.3.98:5173",
        uat: "http://192.168.3.98:5173",
        uatrelease: "https://mclouduat.yyide.com",
        prod: "https://mcloud.yyide.com"
    }
    // 获取公众号openId
    const query = getUrlParams()
    console.log("query:", query)
    if (query.code) {
        system.setOpenId(query.code)
        // 如果存在code并且域名一致不做跳转
        if (!(window.location.href.indexOf(urlObj[query.state]) > -1)) {
            window.location.href = `${urlObj[query.state]}/canteenMachine?code=${query.code}&state=${query.state}`
        }
        getOpenId(query.code)
    } else {
        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx8936b0f9a7bbbfcf&redirect_uri=https%3A%2F%2Fmclouds.yyide.com%2F%23%2FcanteenMachine&response_type=code&scope=snsapi_userinfo&state=${VITE_APP_NAME}`
    }
}

onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    nextTick(async () => {
        userType.value = identityType.value == "eltern" ? 0 : 1
        if (identityType.value == "eltern") {
            state.studentList = user.studentInfo
            state.studentInfo = user.studentInfo[0] || {}
        }
        /* #ifdef H5 */
        if (options.code) {
            getOpenId(options.code)
        } else {
            if (ua.toLowerCase().match(/micromessenger/i) == "micromessenger") {
                await getWeiXinCode()
            }
        }
        /* #endif */
        getweekList()
    })
})
</script>
<style lang="scss" scoped>
.page {
    background: #f9faf9;

    .header {
        padding: 40rpx 30rpx 0;
        background: #fff;

        .title {
            margin-bottom: 32rpx;
            display: flex;
            align-items: center;

            .main_title {
                font-size: 40rpx;
                font-weight: bold;
                color: #333333;
                margin-right: 20rpx;
            }

            .sub_title {
                font-size: 28rpx;
                color: #000;
            }

            .user_btn {
                color: var(--primary-color);
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                display: flex;
                align-items: center;

                .change_over {
                    width: 20px;
                    height: 20px;
                }
            }
        }

        .week_selector {
            display: flex;
            align-items: center;

            .arrow_btn {
                padding: 10rpx;
                display: flex;
                align-items: center;
            }

            .current_week {
                font-size: 28rpx;
                color: #333333;
            }

            .header_btns {
                margin-left: auto;
                display: flex;
                align-items: center;
                font-size: 28rpx;

                .stats_btn {
                    padding: 10rpx 16rpx;
                    border-radius: 16rpx;
                    border: 2rpx solid var(--primary-color);
                    color: var(--primary-color);
                    font-size: 14px;
                }

                .record {
                    color: #ef9808;
                    border: 2rpx solid #ef9808;
                    margin-right: 24rpx;
                }
            }
        }

        .calendar {
            display: flex;
            justify-content: space-between;
            padding: 20rpx 0 30rpx;

            .day_item {
                display: flex;
                flex-direction: column;
                align-items: center;

                .weekday {
                    font-size: 14px;
                    color: #999999;
                    margin-bottom: 10rpx;
                }

                .date {
                    width: 72rpx;
                    height: 72rpx;
                    line-height: 72rpx;
                    text-align: center;
                    font-size: 28rpx;
                    color: #333333;
                    border-radius: 50%;
                    margin-bottom: 10rpx;

                    &.active {
                        background-color: #00c389;
                        color: #ffffff;
                    }
                }

                .dot {
                    width: 8rpx;
                    height: 8rpx;
                    border-radius: 50%;
                    background-color: transparent;
                    margin: 0 auto;
                    box-sizing: border-box;

                    &.has_order {
                        background-color: #00c389;
                    }

                    &.no_order {
                        background-color: #f5222d;
                    }
                }
            }
        }
    }

    .mian_box {
        background: #fff;
        height: 100%;

        :deep(.uv-badge--error) {
            background: #00000000;
            font-weight: normal;
        }

        :deep(.uni-scroll-view) {
            position: static;
        }

        :deep(.uni-scroll-view-content) {
            height: auto;
        }

        .type_title {
            padding: 0 10px;
            font-size: 28rpx;
            color: #999;
            margin: 10px 0;
        }

        .not_set {
            height: calc(100vh - 620rpx);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            .image {
                width: 360rpx;
                height: 210rpx;
            }

            .text {
                font-size: 26rpx;
                font-weight: 400;
                color: #8c8c8c;
                line-height: 36rpx;
                padding-top: 30rpx;
            }
        }

        .tip_box {
            display: flex;
            align-items: center;
            font-size: 28rpx;
            margin: 24rpx 0;
            color: #ffa052;
            padding: 0 20rpx;
        }

        .meal_section {
            background-color: #ffffff;
            color: #333;
            margin-top: 10px;
            margin-bottom: 15px;

            .circle {
                width: 36rpx;
                height: 36rpx;
                border-radius: 50%;
                border: 2rpx solid #b3b3b3;
                margin: 8rpx;
                flex-shrink: 0;
            }

            .meal_header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px;

                .meal_title {
                    font-size: 16px;
                    color: #333333;
                    font-weight: 500;
                    flex: 1;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }

            .unscheduled {
                padding: 20px 10px;
                background-color: #f6f8fa;

                .unscheduled_image {
                    width: 180px;
                    height: 180px;
                }

                font-weight: 400;
                font-size: 11px;
                color: #999999;
                line-height: 16px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
            }

            .dish_list {
                padding: 0 24rpx 0 32rpx;

                .dish_item {
                    display: flex;
                    align-items: center;
                    padding: 20rpx 0;

                    .dish_image {
                        width: 44px;
                        height: 44px;
                        border-radius: 8rpx;
                        margin-right: 24rpx;
                    }

                    .dish_content {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        height: 44px;

                        .dish_name {
                            font-size: 14px;
                            color: #333333;
                            line-height: 1.4;
                        }

                        .dish_price {
                            font-size: 14px;
                            color: #999;
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                        }
                    }
                }
            }

            .extra_section {
                padding-top: 20rpx;

                .extra_title {
                    font-size: 14px;
                    color: #999999;
                    margin-bottom: 20rpx;
                    padding: 0 24rpx 0 32rpx;
                }
            }

            .total_price {
                display: flex;
                justify-content: space-between;
                padding: 30rpx 24rpx 30rpx 32rpx;
                font-size: 28rpx;
                color: #666666;
                margin-top: 20rpx;
                border-top: 2rpx solid #d9d9d9;
            }
        }

        .absoult_box {
            position: absolute;
            bottom: 0px;
            left: 10px;
            width: calc(100% - 20px);
            margin-bottom: 0px;
            z-index: 999;
        }

        .poiner_none {
            pointer-events: none;
            background: #f3f3f3;
        }

        .week_calendar {
            display: flex;
            height: 100%;
            position: relative;

            .left_classify {
                width: 84px;
                min-width: 84px;
                max-width: 84px;
                background: #ffffff;
                height: 100%;
                border-right: 1rpx solid #efefef;

                .classify {
                    width: 84px;
                    height: 48px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-weight: 500;
                    font-size: 12px;
                    color: #262626;
                    line-height: 17px;
                    position: relative;
                }

                .active_classify {
                    color: var(--primary-color);
                    background: #e5fbf4;
                }

                .active_line {
                    position: absolute;
                    left: 0;
                    top: 30%;
                    width: 3px;
                    height: 20px;
                    background: var(--primary-color);
                }

                .classify_status {
                    width: 34px;
                    height: 14px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 8px;
                    border-radius: 2px;
                    font-weight: 400;
                    font-size: 10px;
                    color: #ffffff;
                    line-height: 10px;
                }
            }
            .right_commodity {
                overflow: hidden auto;
                max-height: 100%;
                flex: 1;
                background: #f6f8fa;
                position: relative;
            }

            .nav_btn {
                font-size: 28rpx;
                line-height: 70rpx;
                border-radius: 40rpx;
                margin: 10px 20rpx;
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .btn_prev {
                background-color: #ffffff;
                border: 1px solid #00c389;
                color: #00c389;
            }

            .nextbox {
                background: #f6f8fa;
                // #ifdef H5
                width: 100%;
                position: absolute;
                bottom: 0;
                right: 0;
                // #endif
                // #ifdef MP-WEIXIN
                width: calc(100% - 84px);
                position: fixed;
                bottom: 0;
                right: 0;
                // #endif
            }

            .btn_next {
                background-color: #00c389;
                color: #ffffff;
                line-height: 72rpx;
                display: flex;
                justify-content: space-between;

                .selected_text {
                    font-weight: 400;
                    font-size: 14px;
                    color: #ffffff;
                    line-height: 20px;
                }

                .right_confirm {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 80px;
                    height: 32px;
                    background: #ffffff;
                    border-radius: 16px;
                    font-weight: 500;
                    font-size: 14px;
                    color: var(--primary-color);
                    line-height: 20px;
                }
            }
        }
    }
}

:deep(.zp-paging-container) {
    overflow: hidden;
}

:deep(.zp-paging-container-content) {
    height: 100%;
}
</style>
