<template>
    <qiun-data-charts type="pie" :opts="state.opts" :chartData="props.chartData" />
</template>
<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
const props = defineProps({
    chartData: {
        type: Object,
        default: () => {}
    }
})

const state = reactive({
    opts: {
        color: ["#F98A43", "#5289FB", "#10C683"],
        padding: [5, 5, 5, 5],
        enableScroll: false,
        // tooltipShow: false,
        // tapLegend: false,
        // dataLabel: false,
        padding: [15, 10, 0, 15],
        enableScroll: false,
        legend: {
            show: false
        },
        extra: {
            tooltip: {
                showBox: false
            },
            markLine: {
                dashLength: 2
            },
            pie: {
                activeOpacity: 0.5,
                activeRadius: 3,
                offsetAngle: 0,
                labelWidth: 1,
                border: true,
                borderWidth: 3,
                borderColor: "#FFFFFF",
                customRadius: 35
            }
        }
    }
})
</script>
<style lang="scss" scoped></style>
