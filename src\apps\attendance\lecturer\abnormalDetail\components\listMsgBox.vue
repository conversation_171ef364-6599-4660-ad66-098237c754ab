<template>
    <view class="day_list_box" v-for="(item, index) in list" :key="index">
        <view class="left_box">
            {{ item.requiredDate }}
        </view>
        <view class="right_box">
            <view v-for="(item, index) in item.detailList" :key="index" class="box">
                <view class="left">{{ changeType(item.ruleStatus) }}：{{ item.requiredTime }}</view>
                <view class="right" :style="{ color: change(item.status, 'color') }">{{ change(item.status, "status") }}</view>
            </view>
        </view>
    </view>
</template>

<script setup>
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    type: {
        type: Number,
        default: 0
    }
})

const colorType = {
    0: "var(--primary-color)",
    1: "#F5222D",
    2: "#FC941F",
    3: "#1EC1C3",
    5: "#333333",
    6: "#8C8C8C"
}

const statusType = {
    0: "正常",
    1: "缺勤",
    2: "迟到",
    3: "早退",
    5: "请假",
    6: "未签到"
}

const checkType = {
    color: colorType,
    status: statusType
}

const change = (val, type) => {
    return checkType[type][val] || ""
}

const changeType = (val) => {
    if (props.type === 0) {
        return val === 0 ? "入校" : "离校"
    } else {
        return val === 0 ? "签到" : "签退"
    }
}
</script>

<style lang="scss">
.day_list_box {
    width: 690rpx;
    box-sizing: border-box;
    background: #ffffff;
    margin: 0 auto;
    padding: 20rpx 30rpx;
    display: flex;
    padding-bottom: 40rpx;
    font-size: 28rpx;
    border-bottom: 1rpx solid #d9d9d9;
    padding-right: 0;
    .left_box {
        width: 200rpx;
        font-weight: 600;
        margin-top: 5rpx;
        .head {
            color: #ffffff;
            background-color: var(--primary-color);
            padding: 10rpx 16rpx;
            margin-right: 16rpx;
            border-radius: 50%;
        }
    }
    .right_box {
        flex: 1;
        margin: auto 0;
        .box {
            display: flex;
            flex: 1;
            justify-content: space-around;
            line-height: 50rpx;
            .left {
                flex: 1;
                text-align: center;
            }
            .right {
                width: 100rpx;
                text-align: left;
            }
        }
    }
}
</style>
