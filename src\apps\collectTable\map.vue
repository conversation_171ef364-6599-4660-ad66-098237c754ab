<!-- collectTable 收集表 -->
<template>
    <view class="maps" :class="{ active: !state.isAutoLocate }">
        <view id="container"></view>
        <view class="select_input" v-if="state.selectShow">
            <uni-easyinput class="reset" type="text" id="tipinput" :inputBorder="false" :border="false" :clearable="false" v-model="state.cityinfo" placeholder="请输入" />
            <button class="btn" type="primary" style="background-color: var(--primary-color); color: #fff" size="mini" @click="saveMap">确定</button>
        </view>
    </view>
</template>

<script setup>
import { onMounted, onBeforeUnmount, reactive, shallowRef, nextTick, watch } from "vue"
// #ifdef H5 || H5-WEIXIN
import AMapLoader from "@amap/amap-jsapi-loader"
// #endif

const emit = defineEmits(["emitSaveMap"])
const props = defineProps({
    paramsAddress: {
        type: Object,
        default: () => {}
    },

    isAutoLocate: {
        type: Boolean,
        default: true
    }
})
const state = reactive({
    isAutoLocate: false,
    selectShow: false,
    newAddress: { chain: "", address: "", geo: "", lat: "" },
    address: {
        chain: "",
        address: "",
        geo: "",
        lat: ""
    },
    placeSearch: null,
    autoComplete: null,
    tipinputValue: "",
    cityinfo: "",
    center: [12672296.910942225, 2582976.932362227],
    key: "41a32b85bc37cf2fe1b0ed049874f5ce",
    securityKey: "http://*************:8035/_AMapService/api" //安全密钥
})
const map = shallowRef(null)

// 将创建的点标记添加到已有的地图实例：
const initMap = () => {
    // #ifdef H5 || H5-WEIXIN
    AMapLoader.load({
        key: state.key, // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        viewMode: "3D", // 地图模式
        pitch: 75, // 地图俯仰角度，有效范围 0 度- 83 度
        terrain: true, // 开启地形图
        enableHighAccuracy: true
    })
        .then((AMap) => {
            map.value = new AMap.Map("container", {
                resizeEnable: true,
                terrain: true, // 开启地形图
                viewMode: "3D", //是否为3D地图模式
                // center: state.center, //初始地图中心点
                zoom: 11
            })
            const MarkerFn = () => {
                let marker = new AMap.Marker({
                    position: map.value.getCenter(),
                    icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
                    offset: new AMap.Pixel(-10, -30),
                    // 设置是否可以拖拽
                    draggable: true,
                    cursor: "move"
                })

                marker.setMap(map.value)
            }

            // 查询
            const searchFn = () => {
                var auto = new AMap.AutoComplete({
                    input: "tipinput"
                })
                var placeSearch = new AMap.PlaceSearch({
                    map: map.value
                }) //构造地点查询类

                auto.on("select", (e) => {
                    const { location, adcode, district, name } = e.poi
                    placeSearch.setCity(adcode)
                    placeSearch.search(name) //关键字查询查询
                    state.cityinfo = `${district}${name}`
                    state.center = location
                    state.address = {
                        chain: district,
                        address: name,
                        geo: location.lng,
                        lat: location.lat
                    }
                    MarkerFn()
                }) //注册监听，当选中某条记录时会触发
            }

            //获取用户所在城市信息
            const showCityInfo = () => {
                //实例化城市查询类
                var citysearch = new AMap.CitySearch()

                //自动获取用户IP，返回当前城市
                citysearch.getLocalCity(function (status, result) {
                    const { info, bounds, city, province } = result
                    if (status === "complete" && info === "OK") {
                        if (result && city && bounds) {
                            state.address = {
                                chain: province,
                                address: city,
                                geo: bounds.northEast.lng,
                                lat: bounds.northEast.lat
                            }
                            state.cityinfo = city
                            //地图显示当前城市
                            map.value.setBounds(bounds)
                        }
                    } else {
                        console.log("您当前所在城市：" + info)
                    }
                })
            }

            AMap.plugin(["AMap.Geolocation", "AMap.CitySearch", "AMap.Marker", "AMap.PlaceSearch", "AMap.AutoComplete"], function () {
                // if (state.newAddress.chain || state.newAddress.address) {
                const { chain, address } = state.newAddress
                const placeSearch = new AMap.PlaceSearch({
                    map: map.value, // 展现结果的地图实例
                    panel: "panel", // 结果列表将在此容器中进行展示。
                    autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
                })
                state.cityinfo = chain + address
                // + (props.detailedAddress || '')
                //关键字查询
                placeSearch.search(chain + address)
                if (!props.isAutoLocate) {
                    const autoOptions = {
                        city: "全国"
                    }
                    let autoComplete = new AMap.Autocomplete(autoOptions)
                    autoComplete.search(state.cityinfo, function (status, result) {
                        // 搜索成功时，result即是对应的匹配数据
                        if (status === "complete" && result.info === "OK") {
                            if (result.tips && result.tips.length) {
                                result.tips.every((v) => {
                                    if (v.location.lat) {
                                        state.address = {
                                            chain: v.district,
                                            address: address || v.name,
                                            geo: v.location.lng,
                                            lat: v.location.lat
                                        }
                                        return false
                                    }
                                    return true
                                })
                            }
                            saveMap()
                            //地图显示当前城市
                        } else {
                            saveMap()
                        }
                    })
                }
                // } else {
                //     // 定位标签
                //     MarkerFn()
                // }
                //输入提示
                searchFn()
                // —————————————————— 自动定位 ————————————————————————
                // showCityInfo();
            })
            //解析定位结果
        })
        .catch((e) => {
            console.log(e)
        })
    // #endif
}
const saveMap = () => {
    console.log("emitSaveMap", state.address)
    // state.isAutoLocate = false
    emit("emitSaveMap", state.address)
    // map.value.destroy();
}
watch(
    () => props.paramsAddress,
    (val) => {
        state.address = val
        state.newAddress = val
    },
    { immediate: true, deep: true }
)

watch(
    () => props.isAutoLocate,
    (val) => {
        state.isAutoLocate = val
    },
    { immediate: true, deep: true }
)
onBeforeUnmount(() => {
    // map.value.destroy();
})

// // 销毁地图，并清空地图容器
onMounted(() => {
    // map.value.destroy();/
    state.selectShow = true
    window._AMapSecurityConfig = {
        serviceHost: state.securityKey
    }
    nextTick(() => {
        initMap()
    })
})
</script>

<style scoped lang="scss">
.maps {
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 99;
    padding-top: 92rpx;

    &.active {
        z-index: -1;
    }

    .select_input {
        display: flex;
        background: #ffffff;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;

        .btn {
            // width: 100rpx;
            margin: 16rpx;
        }
    }
}

#container {
    width: 100%;
    height: 100%;
}
</style>
