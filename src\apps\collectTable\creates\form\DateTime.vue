<!-- DateTime 日期 -->
<template>
    <view>
        <text>
            <uni-icons type="plus" size="20"></uni-icons>
            填写格式: 年-月-日
            {{ DateTimeText(element) ? "时:分" : "" }}
        </text>
        <view>
            <checkbox-group @change="handleTimeDivision($event, element)">
                <checkbox :value="state.isTimeDivision" :checked="state.isTimeDivision" activeBackgroundColor="var(--primary-color)" iconColor="#fff" :key="element.id" style="transform: scale(0.8)">时分</checkbox>
            </checkbox-group>
        </view>
    </view>
</template>

<script setup>
import { reactive, inject } from "vue"
const props = defineProps({
    element: {
        type: Object,
        default: () => {}
    },
    index: {
        type: Number,
        default: 0
    }
})

const state = reactive({
    isTimeDivision: false
})
const DateTimeText = computed(() => {
    return (element) => element.props.format == "YYYY-MM-DD HH:mm"
})
const handleTimeDivision = (evt, element) => {
    element.props.format = !!evt.detail.value.length ? "YYYY-MM-DD HH:mm" : "YYYY-MM-DD"
}
</script>

<style scoped lang="scss"></style>
