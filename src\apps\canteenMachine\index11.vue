<template>
    <div>
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe ref="myIframe" class="webview" :src="src" frameborder="0" @error="onIframeError" @load="onIframeLoad"></iframe>
        <div v-if="state.loadError" class="error-message">
            内容加载失败，请刷新重试或检查网络连接
            <button @click="reloadIframe">重新加载</button>
        </div>
        <div v-if="state.loading" class="loading-message">加载中...{{ src.value }}</div>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS-->
        <web-view class="webview" ref="myIframe" :src="src"></web-view>
        <!-- #endif -->
    </div>
</template>

<script setup>
// #ifdef H5-WEIXIN || H5
import { inject } from "vue"
import { getToken } from "@/utils/storageToken.js"
import { checkPlatform } from "@/utils/sendAppEvent.js"
const token = getToken()?.replace("Bearer ", "")
const { system, user } = store()

const state = reactive({
    loadError: false,
    loading: true
})

const userType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "parent"
    } else {
        return "teacher"
    }
})

const src = ref("")
if (!token) {
    uni.reLaunch({ url: "/pages/login/index" })
}
const { VITE_BASE_URL, VITE_BASE_APPLET } = import.meta.env
const myIframe = ref(null)

function onIframeLoad() {
    state.loading = false
    state.loadError = false
}

function onIframeError() {
    state.loading = false
    state.loadError = true
}

function reloadIframe() {
    state.loading = true
    state.loadError = false
    myIframe.value.src = src.value
}

function onBridgeReady(params) {
    window.WeixinJSBridge.invoke("getBrandWCPayRequest", params, function (res) {
        if (res.err_msg == "get_brand_wcpay_request:ok") {
            console.log("支付成功")
            myIframe.value.postMessage({ result: "success" }, "*")
            setTimeout(() => {
                window.location.href = VITE_BASE_URL
            }, 600)
        } else {
            console.log("支付失败")
            myIframe.value.postMessage({ result: "error" }, "*")
        }
    })
}

// 监听来自 iframe 的消息
window.addEventListener("message", function (event) {
    if (event.data.action === "ready") {
        onBridgeReady(event.data.data)
    }
    if (event.data.action === "back") {
        uni.switchTab({
            url: "/pages/workbench/index"
        })
    }
})

const getUrlParams = (isShare, _url) => {
    const params = {}
    const url = _url || window.location.search || window.location.hash
    if (url.indexOf("?") > -1) {
        const str = url.split("?")[1]
        const arr = str.split("&")
        arr.forEach((item) => {
            const [key, value] = item.split("=")
            params[key] = value
        })
        return params
    } else {
        return params
    }
}

// 判断不同设备
let ua = navigator.userAgent
// 获取微信code
function getWeiXinCode() {
    const VITE_APP_NAME = import.meta.env.VITE_APP_NAME
    const urlObj = {
        dev: "http://192.168.3.158:8009",
        uat: "http://192.168.3.158:8009",
        uatrelease: "https://mclouduat.yyide.com",
        prod: "https://mcloud.yyide.com"
    }
    // 获取公众号openId
    const query = getUrlParams()
    console.log("query:", query)
    if (query.code) {
        system.setOpenId(query.code)
        // 如果存在code并且域名一致不做跳转
        if (!(window.location.href.indexOf(urlObj[query.state]) > -1)) {
            window.location.href = `${urlObj[query.state]}/canteenMachine?code=${query.code}&state=${query.state}`
        }
    } else {
        window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx8936b0f9a7bbbfcf&redirect_uri=https%3A%2F%2Fmcloud.yyide.com%2F%23%2FcanteenMachine&response_type=code&scope=snsapi_userinfo&state=${VITE_APP_NAME}`
    }
}

// 如果是公众号嵌套前提前获取openid
if (ua.toLowerCase().match(/micromessenger/i) == "micromessenger") {
    getWeiXinCode()
}

const code = computed(() => system.openId)
//设置Iframe的src
const isDingtalkAuth = inject("isDingtalkAuth")

console.log("-----", isDingtalkAuth)

// 免密登录，钉钉环境下隐藏底部导航栏
const hideNavBar = isDingtalkAuth == 1 && checkPlatform() == "dingding" ? 0 : 1

src.value = `${decodeURIComponent(VITE_BASE_APPLET)}/smartOrder/index?token=${token}&code=${code.value}&type=canteenMachine&userType=${userType.value}&hideNavBar=${hideNavBar}`

console.log("iframe__", src.value)
// #endif
</script>

<style lang="scss" scoped>
.webview {
    height: 100vh;
    width: 100vw;
}

.error-message,
.loading-message {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f5f5f5;
    color: #666;
}

.error-message {
    color: #f56c6c;
}

button {
    background-color: #00c389;
    color: #fff;
    font-size: 14px;
    margin-top: 8px;
}
</style>
