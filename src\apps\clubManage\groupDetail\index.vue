<template>
    <view>
        <z-paging class="container_box">
            <template #top>
                <view class="header">
                    <uni-nav-bar left-icon="left" backgroundColor="transparent" fixed statusBar title="社团详情" @clickLeft="back" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()" />
                    <view class="header_box">
                        <view class="l">
                            <img class="img" :src="state.info.iconUrl" />
                            <view class="flag" :style="{ background: state.info.level === 'department' ? 'var(--primary-color)' : '#FF992B' }">{{ state.info.level === "department" ? "院" : "校" }}</view>
                        </view>
                        <view class="r">
                            <view class="top">{{ state.info.name }}</view>
                            <view class="main">{{ state.info.slogan }}</view>
                            <view class="btm">
                                <view class="left">分类: {{ state.info.categoryName }}</view>
                                <view @click="handleMember" style="flex-shrink: 0">成员: {{ state.info.memberCount }}人<uni-icons type="right" size="14" color="#fff"></uni-icons></view>
                            </view>
                        </view>
                    </view>
                    <view class="modal_box"></view>
                </view>
            </template>
            <view class="main_box">
                <view class="label">社团负责人</view>
                <view class="text">{{ state.info.ownerName }}</view>
                <view class="label">指导老师</view>
                <view class="text">{{ guidanceTeacher }}</view>
                <view class="label">社团简介</view>
                <view class="text">{{ state.info.description }}</view>
            </view>
            <template #bottom v-if="Object.keys(state.info).length">
                <view class="footer">
                    <view v-if="state.info.memberRole === 'none'" style="flex: 1">
                        <button class="btnR" v-if="state.info.memberStatus === 'none'" :style="{ background: 'var(--primary-color)' }" @click="handleEdit('join')">申请加入</button>
                        <button class="btnR" v-if="state.info.memberStatus === 'requested'" :style="{ background: '#FFAD3C' }">审核中</button>
                        <button class="btnR" v-if="state.info.memberStatus === 'joined'" :style="{ background: '#F5222D' }" @click="handleEdit('out')">退出社团</button>
                    </view>
                    <view v-else style="display: flex; flex: 1; justify-content: flex-end">
                        <button class="btnL" style="flex: 1; background: #f5222d; color: #fff" @click="handleEdit('out')" v-if="state.info.memberRole !== 'owner'">退出社团</button>
                        <button class="btnR" v-if="state.info.memberRole === 'owner' || state.info.memberRole === 'admin'" :style="{ background: 'var(--primary-color)' }" @click="handleEdit('edit')">编辑社团</button>
                    </view>
                </view>
            </template>
        </z-paging>
        <view class="addNotice" @click="handleShare">
            <img src="@nginx/workbench/groupManage/share.png" class="img" alt="" />
            <view>分享</view>
        </view>
        <!-- 分享弹窗 -->
        <uni-popup ref="popup" background-color="#fff" type="bottom" :mask-click="false" class="pop_box">
            <uni-icons type="closeempty" size="22" class="close" @click="close"></uni-icons>
            <view class="header_box">分享</view>
            <view class="pop_main" @click="copy">
                <view class="popup_content">
                    <img src="@nginx/workbench/groupManage/link.png" class="img" />
                </view>
                <view>复制链接</view>
            </view>
        </uni-popup>

        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog class="dialog_box" type="info" cancelText="取消" confirmText="确定" :content="state.content" @confirm="dialogConfirm" @close="dialogClose"> </uni-popup-dialog>
        </uni-popup>
    </view>
</template>
<script setup>
import useStore from "@/store"
const { local, system } = useStore()
import { copyUrl } from "@/utils"

const state = reactive({
    info: {},
    content: "确定要申请加入本社团吗?",
    isGroupIn: false,
    status: "join"
})

onLoad((options) => {
    getDetail(options.id)
})

onUnmounted(() => {
    if (local.share) {
        local.clear()
    }
})

// 获取社团详情
function getDetail(id) {
    http.post("/app/club/club/get", { id }).then((res) => {
        state.info = res.data
    })
}

const guidanceTeacher = computed(() => {
    if (state.info.advisors) {
        return state.info.advisors.map((i) => i.name).join("、")
    } else {
        return ""
    }
})

const popup = ref(null)

const handleShare = () => {
    popup.value.open()
}

const alertDialog = ref(null)
const handleEdit = (val) => {
    state.status = val
    if (val == "join") {
        state.content = "确定要申请加入本社团吗?"
        alertDialog.value.open()
    } else if (val == "out") {
        state.content = "确定要退出本社团吗?"
        alertDialog.value.open()
    } else {
        navigateTo({ url: "/apps/clubManage/applyGroup/index", query: { oldId: state.info.id, isEdit: true } })
    }
}

// 弹窗确认
const dialogConfirm = () => {
    if (state.status == "join") {
        http.post("/app/club/join-request/create", { clubId: state.info.id }).then((res) => {
            uni.showToast({ title: "申请成功", icon: "none" })
            getDetail(state.info.id)
            dialogClose()
        })
    } else {
        http.post("/app/club/member/quit", { clubId: state.info.id }).then((res) => {
            uni.showToast({ title: "退出社团成功", icon: "none" })
            getDetail(state.info.id)
            dialogClose()
        })
    }
}

// 弹窗关闭
const dialogClose = () => {
    alertDialog.value.close()
}

// 查看成员
const handleMember = () => {
    navigateTo({
        url: "/apps/clubManage/viewMembers/index",
        query: {
            clubId: state.info.id,
            memberRole: state.info.memberRole
        }
    })
}

const back = () => {
    if (local.share) {
        console.log(local.share)
        local.clear()
        uni.reLaunch({ url: system.tabBarList[0]?.pagePath })
    } else {
        uni.navigateBack()
    }
}

// 复制url
const copy = () => {
    const url = `${import.meta.env.VITE_BASE_SHARE}/pages/sharePage/index?type=groupDetail&id=${state.info.id}`
    copyUrl(url)
}

const close = () => {
    popup.value.close()
}
</script>
<style lang="scss" scoped>
.container_box {
    .header {
        box-sizing: border-box;
        background-image: url("https://file.1d1j.cn/yide-applet/groupManage/bg-stxq.png");
        position: relative;
        :deep(.uni-navbar__content) {
            background: transparent !important;
            border: none;
            .uniui-left {
                color: #fff !important;
            }
            .uni-navbar__header {
                background: transparent !important;
                .uni-nav-bar-text {
                    color: #fff !important;
                    font-weight: 600;
                }
            }
        }
        .header_box {
            margin-bottom: 20rpx;
            padding: 30rpx;
            box-sizing: border-box;
            display: flex;
            .l {
                position: relative;
                width: 160rpx;
                height: 160rpx;
                border-radius: 16rpx;
                overflow: hidden;
                margin-right: 24rpx;
                .img {
                    width: 100%;
                    height: 100%;
                }
                .flag {
                    font-size: 16rpx;
                    color: $uni-text-color-inverse;
                    position: absolute;
                    top: 0;
                    width: 24rpx;
                    height: 24rpx;
                    text-align: center;
                    border-radius: 0 0 12rpx 0;
                }
            }
            .r {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                flex: 1;
                overflow: hidden;
                color: $uni-text-color-inverse;
                .top {
                    font-size: 36rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .main {
                    font-size: 24rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .btm {
                    font-size: 24rpx;
                    display: flex;
                    align-items: center;
                    .left {
                        padding-right: 16rpx;
                        border-right: 2rpx solid #fff;
                        margin-right: 16rpx;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
        .modal_box {
            width: 100%;
            height: 20rpx;
            background: #fff;
            position: absolute;
            bottom: 0;
            left: 0;
            border-radius: 20rpx 20rpx 0 0;
        }
    }
    .main_box {
        background-color: $uni-text-color-inverse;
        border-radius: 20rpx 20rpx 0rpx 0rpx;
        box-sizing: border-box;
        padding: 40rpx 30rpx;
        padding-bottom: 188rpx;
        font-size: 24rpx;
        .label {
            color: #595959;
            margin-bottom: 16rpx;
        }
        .text {
            color: #999;
            margin-bottom: 32rpx;
        }
    }
    .footer {
        padding: 0 30rpx;
        background: #fff;
        font-size: 32rpx;
        display: flex;
        box-sizing: border-box;
        .btnL {
            color: #f5222d;
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            background: #fff;
            width: 200rpx;
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            font-size: 32rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 0;
            &:after {
                border: 2rpx solid #f5222d;
            }
            .img {
                width: 44rpx;
                height: 44rpx;
            }
        }
        .btnR {
            color: $uni-text-color-inverse;
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            flex: 1;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            margin-left: 30rpx;
            font-size: 32rpx;
            &:after {
                border: none;
            }
        }
    }
}
.addNotice {
    width: 112rpx;
    height: 112rpx;
    background: var(--primary-color);
    position: fixed;
    right: 30rpx;
    bottom: 310rpx;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: -2px 3px 6px 1px #d6f3e8;
    font-size: 24rpx;
    color: $uni-text-color-inverse;
    .img {
        width: 56rpx;
        height: 56rpx;
    }
}
.pop_box {
    position: relative;
    .close {
        position: absolute;
        top: 30rpx;
        right: 30rpx;
    }
    .header_box {
        text-align: center;
        font-size: 32rpx;
        color: #262626;
        padding: 32rpx 0;
    }
    .pop_main {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 40rpx 0 88rpx 0;
        .popup_content {
            width: 88rpx;
            height: 88rpx;
            background-color: #f75251;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            .img {
                width: 48rpx;
                height: 48rpx;
            }
        }
    }
}
.dialog_box {
    :deep(.uni-dialog-title) {
        display: none;
    }
    :deep(.uni-dialog-content) {
        padding-top: 88rpx;
        padding-bottom: 88rpx;
    }
    :deep(.uni-button-color) {
        color: var(--primary-color) !important;
    }
}
</style>
