<template>
    <view class="week_container_box">
        <view class="top_box">
            <view class="up_box">{{ title }}</view>
            <SelectBtn :list="list" v-model:value="value" @handleClick="selectClick"></SelectBtn>

            <Swiper v-if="state.swiperList.length" v-model:current="current" @change="swiperChange" :list="state.swiperList"> </Swiper>
        </view>
        <view class="msg_box">
            <ListMsgBox :show="false" :list="dataList" @handleDetailClick="handleDetailClick"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>
    </view>
</template>

<script setup>
import Swiper from "./components/swiper.vue"
import ListMsgBox from "../../components/listMsgBox.vue"
import SelectBtn from "../../components/selectBtn.vue"
import useQueryList from "../../hook/useQueryList.js"
import { onReachBottom } from "@dcloudio/uni-app"
import { getMonthWeekList, getWeekOfMonth } from "@/utils/getDate"

const { getList, dataList, pageNo, status } = useQueryList()

const { weekOfMonth, startOfWeek, endOfWeek } = getWeekOfMonth()
const timeGroup = capitalize(startOfWeek) + " - " + capitalize(endOfWeek).slice(5)
const list = ref(getMonthWeekList())
const value = ref(weekOfMonth)
let title = ref(timeGroup)
let current = ref(0)

const state = reactive({
    typeList: [
        { name: "出入校考勤", value: 0, isCheck: true },
        { name: "事件考勤", value: 1 },
        { name: "课程考勤", value: 2 }
    ],
    swiperList: []
})

function getPageList(params) {
    return http.post("/app/teach/attendance/timetableDatePage", params)
}

// 获取周月统计头部
async function getTimeTableDate() {
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate
    }

    const res = await http.post("/app/teach/attendance/timetableDate", params)
    state.swiperList = res.data

    if (res.data.length) {
        let arg = {
            classesId: state.swiperList[current.value].classesId,
            ...params
        }

        getList(getPageList, arg)
    }
}

getTimeTableDate()

const handleDetailClick = (val) => {
    const params = {
        navTitle: val.studentName,
        classesName: val.classesName,
        title: `${list.value[value.value].name} | ${title.value}`,
        params: JSON.stringify({
            attendanceId: val.attendanceId,
            classesId: val.classesId,
            endDate: val.endDate,
            startDate: val.startDate,
            type: val.type,
            userId: val.userId
        })
    }
    navigateTo({
        url: "/apps/attendance/lecturer/abnormalDetail/index",
        query: params
    })
}

const selectClick = (val) => {
    title.value = capitalize(val.startDate) + " - " + capitalize(val.endDate).slice(5)
    dataList.value = []
    pageNo.value = 1
    getTimeTableDate()
}

function capitalize(date) {
    return date.replace("-", "年").replace("-", "月") + "日"
}

const swiperChange = (val) => {
    dataList.value = []
    pageNo.value = 1
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate,
        classesId: state.swiperList[current.value].classesId
    }
    getList(getPageList, params)
}

onReachBottom(() => {
    const time = list.value[value.value - 1]
    const params = {
        startDate: time.startDate,
        endDate: time.endDate,
        classesId: state.swiperList[current.value].classesId
    }
    getList(getPageList, params)
})
</script>

<style lang="scss">
.week_container_box {
    background-color: $uni-bg-color-grey;
    .top_box {
        .up_box {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
            background-color: var(--primary-bg-color);
            border-bottom: 1rpx solid #d9d9d9;
        }
    }
    .msg_box {
        background-color: #fff;
        margin-bottom: 20rpx;
    }
}
</style>
