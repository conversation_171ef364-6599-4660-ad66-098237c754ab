const baseOption = {
    title: "",
    content: "",
    showCancel: true,
    cancelText: "取消",
    cancelColor: "#999",
    confirmText: "确定",
    confirmColor: "var(--primary-color)",
    editable: false,
    placeholderText: "请输入"
}
export default (params) => {
    uni.showModal({
        ...baseOption,
        ...params,
        success: function (res) {
            if (res.confirm) {
                params.success()
            } else if (res.cancel) {
                if (!params.cancel) return
                params.cancel()
            }
        },
        fail: (e) => {
            if (!params.fail) return
            params.fail()
        },
        complete: (e) => {
            if (!params.complete) return
            params.complete()
        }
    })
}
