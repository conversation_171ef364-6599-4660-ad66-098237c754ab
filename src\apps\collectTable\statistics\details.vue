<!-- details 统计详情 -->
<template>
    <z-paging ref="paging" :refresher-only="true" :refresher-enabled="isRefresher" @onRefresh="getDetail" :auto="false">
        <template #top>
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="统计详情" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
        </template>
        <view class="details">
            <view class="cell">
                <view class="cell-handle">
                    <view class="title">{{ state.detail.title }}</view>
                    <uni-icons type="more-filled" color="#b8b8b8" size="20" @click="popup.open('bottom')"></uni-icons>
                </view>
                <view class="roster">
                    <text class="item">创建人： {{ state.detail.creator }}</text>
                </view>
                <view class="roster">
                    <text class="item">创建时间： {{ state.detail.createTime }}</text>
                </view>
                <view class="roster">
                    <text class="item">收集范围： {{ scope }}</text>
                </view>
            </view>

            <view class="cell">
                <view class="cell-handle">
                    <view class="title">数据统计</view>
                    <view class="time" v-if="state.detail.collectMethod == 0" @click="openDate">
                        <text>{{ state.detail.answerStatistics.time }}</text>
                        <uv-icon style="padding-left: 6px" name="play-right-fill" color="var(--primary-color)" size="12"></uv-icon>
                    </view>
                </view>
                <view class="look_staus_end">
                    <view class="look" @click="handerDetailed(1)">
                        <view class="look_num" style="color: var(--primary-color)">
                            {{ state.detail?.answerStatistics?.todayAlreadyDid }}
                        </view>
                        <view class="look_title">已提交</view>
                        <text class="look_btn">查看明细</text>
                    </view>
                    <view class="look" @click="handerDetailed(0)">
                        <view class="look_num" style="color: #ffc328ff">
                            {{ state.detail?.answerStatistics?.todayGonnaDo }}
                        </view>
                        <view class="look_title">未提交</view>
                        <text class="look_btn">查看明细</text>
                    </view>
                </view>
            </view>
            <view style="background-color: #fff">
                <uni-collapse class="reset-collapse" ref="collapse" accordion v-for="item in state.detail?.questionStatistics" :key="item.no">
                    <uni-collapse-item>
                        <template v-slot:title>
                            <view class="reset-uni-list">
                                <view class="title">
                                    <text class="order_color">{{ item.no }}. </text>
                                    <text>{{ item.name }}</text>
                                </view>
                                <view class="count">{{ `${item.count}份` }}</view>
                            </view>
                        </template>
                        <view class="content">
                            <uni-list-item class="reset-uni-list" v-for="(it, idx) in item.answerList" :key="idx" :rightText="`${it.count}份`" :title="it.description"> </uni-list-item>
                        </view>
                    </uni-collapse-item>
                </uni-collapse>
            </view>

            <!-- 日期 -->
            <uv-datetime-picker ref="datetimePicker" v-model="state.datetime" mode="date" :minDate="state.minDate" :maxDate="state.maxDate" @confirm="onConfirm"> </uv-datetime-picker>
            <!-- 普通弹窗 -->
            <uni-popup ref="popup" background-color="#fff" borderRadius="20rpx 20rpx 0 0">
                <view class="popup-content">
                    <view class="handle">
                        <view class="handle-title"> <text class="order_color"> 设置 </text></view>
                        <uni-icons class="handle-icon" type="closeempty" size="16" @click="popup.close()"></uni-icons>
                    </view>
                    <view class="content">
                        <uni-list-item
                            :show-extra-icon="true"
                            :extra-icon="{
                                size: '20',
                                type: 'compose'
                            }"
                            title="结束此上报"
                            clickable
                            @click="handerCancel('end')"
                        />
                        <uni-list-item :show-extra-icon="true" clickable @click="handerCancel('delete')">
                            <template v-slot:header>
                                <text style="color: #ff0000; font-size: 28rpx"><uni-icons color="#ff0000" type="trash" size="20"></uni-icons> 删除此上报</text>
                            </template>
                        </uni-list-item>
                    </view>
                </view>
            </uni-popup>
            <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
                <view
                    :style="{
                        padding: '33px 0px 10px 0px'
                    }"
                    >{{ "确定删除？" }}</view
                >
            </yd-popup>
        </view>
        <template #empty>
            <yd-empty v-if="state.activeTab" text="暂无数据" />
        </template>
    </z-paging>
</template>

<script setup>
import dayjs from "dayjs"

const isRefresher = ref(true)
const accordionVal = ref(3)
const paging = ref(null)

const confirmRef = ref()
const popup = ref()

const datetimePicker = ref()
const state = reactive({
    vanLoading: false,
    minDate: new Date(1900, 10, 1),
    maxDate: new Date(),
    datetime: new Date(),
    detail: {
        createTime: "",
        collectMethod: 0,
        answerStatistics: { time: "", todayGonnaDoPersonList: [] }
    },
    dataList: [],
    detailId: ""
})

const getDetail = () => {
    state.vanLoading = true
    const { time } = state.detail.answerStatistics
    const params = {
        id: state.detailId,
        time
    }
    http.post("/app/collectTableStatistics/statisticsDetail", params)
        .then(({ data }) => {
            const {
                answerStatistics: { time = "" },
                answerTimeList
            } = data
            state.detail = data
            let minDate = answerTimeList[answerTimeList.length - 1].time
            state.minDate = new Date(minDate)
            state.datetime = new Date(time)
            paging.value.complete()
        })
        .finally(() => (state.vanLoading = false))
        .catch(() => {
            paging.value.complete(false)
        })
}

const scope = computed(() => {
    return state.detail?.scope?.join(",")
})

const orderNumber = (order) => {
    let num = order + 1
    return `${num > 9 ? num : "0" + num}.`
}
// 跳收集明细
const handerDetailed = (operation) => {
    navigateTo({
        url: "/apps/collectTable/statistics/detailed",
        query: {
            id: state.detailId,
            operation,
            time: state.detail.answerStatistics.time
        }
    })
}
const handerCancel = (item) => {
    if (item === "delete") {
        confirmRef.value.open()
    } else {
        http.post("/app/collectTableStatistics/finish", { id: state.detail.id }).then(({ message }) => {
            uni.showToast({ title: message })
            popup.value.close()
        })
    }
}
// 确定删除统计
const dialogConfirm = (api) => {
    http.post("/app/collectTableStatistics/delete", { id: state.detail.id }).then(({ message }) => {
        uni.showToast({ title: message })
        navigateTo({
            url: "/apps/collectTable/index",
            query: {
                activeTab: "statistics"
            }
        })
    })
}

function openDate() {
    isRefresher.value = false
    datetimePicker.value.open()
}
// 数据统计日期
const onConfirm = (item) => {
    state.detail.answerStatistics.time = dayjs(item.value).format("YYYY-MM-DD")
    isRefresher.value = true
    paging.value.refresherEnabled = true
    datetimePicker.value.close()
    paging.value.reload()
}
function clickLeft() {
    uni.navigateBack()
}

onLoad((item) => {
    state.detailId = item.id
    getDetail()
})
</script>

<style scoped lang="scss">
@import "../style.scss";

.details {
    padding: 5rpx 0;
    @include autoHeight(65rpx);
    overflow: hidden auto;

    .reset-collapse {
        border-bottom: 20rpx solid #f6f6f6;

        .reset-uni-list {
            position: relative;
            margin: 10rpx 0 !important;
            .title {
                padding: 0 60rpx 0 30rpx;
                word-wrap: break-word;
            }

            .count {
                font-size: 28rpx;
                color: #999;
                position: absolute;
                top: 6rpx;
                right: 10rpx;
            }
        }
    }

    .cell {
        margin: 20rpx auto;
        padding: 30rpx;
        background: $white-color;

        .cell-handle {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                font-weight: 600;
                @include fontSize(36rpx);
            }

            .three-point {
                color: #ffffff;
                background: #d8d8d8;
                border-radius: 15rpx;
            }

            .time {
                @include fontSize(28rpx);
                display: flex;
                align-items: center;
                min-height: 30rpx;
                min-width: 150rpx;
                text-align: right;
            }
        }

        .roster {
            margin: 20rpx auto;

            .item {
                @include fontSize(26rpx);
            }
        }
    }

    .look_staus_end {
        display: flex;
        flex: 1;
        justify-content: space-around;
        margin: 30rpx auto;

        .look {
            width: 330rpx;
            text-align: center;
            padding: 30rpx;
            border-radius: 10rpx;
            box-sizing: border-box;
            height: 260rpx;
            background: url(https://file.1d1j.cn/cloud-mobile/components/statistics_1.png) no-repeat;
            background-size: 100% 100%;

            &:last-child {
                margin-left: 30rpx;
                background: url(https://file.1d1j.cn/cloud-mobile/components/statistics_0.png) no-repeat;
                background-size: 100% 100%;
            }

            .look_num {
                font-weight: 600;
                @include fontSize(48rpx);
            }

            .look_title {
                margin: 26rpx auto;
                @include fontSize(26rpx);
                font-weight: 400;
            }

            .look_btn {
                @include fontSize(26rpx);
                background: $white-color;
                color: var(--primary-color);
                border-radius: 10rpx;
                font-weight: 500;
                width: 144rpx;
                height: 52rpx;
                line-height: 52rpx;
                margin: 0 auto;
                display: block;
            }
        }
    }

    .reset-uni-list {
        :deep(.uni-list-item__container) {
            display: flex;
            align-items: center;
        }
    }
}

.popup-content {
    .handle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx;

        .handle-title {
            font-size: 40rpx;
            font-weight: 500;
            text-align: center;
            flex: 1;
        }

        .handle-icon {
            width: 40rpx;
        }
    }

    .content {
        .uni-list-cell {
            display: flex;
            align-items: center;
            padding: 20rpx;
            border-bottom: 1rpx solid #eee;
        }
    }
}
</style>
